import 'package:flutter_test/flutter_test.dart';
import 'package:voice_assistant/services/maps_service.dart';
import 'package:latlong2/latlong.dart';

void main() {
  group('MapsService', () {
    late MapsService mapsService;

    setUp(() {
      mapsService = MapsService();
    });

    test('should initialize correctly', () {
      expect(mapsService.isNavigating, false);
      expect(mapsService.routePoints, isEmpty);
      expect(mapsService.routePolyline, isNull);
    });

    test('should handle geocoding for known cities', () async {
      // Test avec Paris
      final parisCoords = await mapsService.getCoordinatesFromAddress('Paris');
      expect(parisCoords, isNotNull);
      expect(parisCoords!.latitude, closeTo(48.8566, 0.1));
      expect(parisCoords.longitude, closeTo(2.3522, 0.1));
    });

    test('should handle geocoding fallback for Paris', () async {
      // Test du fallback pour Paris
      final parisCoords = await mapsService.getCoordinatesFromAddress('paris france');
      expect(parisCoords, isNotNull);
      // Le fallback devrait retourner les coordonnées exactes de Paris
      expect(parisCoords!.latitude, equals(48.8566));
      expect(parisCoords.longitude, equals(2.3522));
    });

    test('should handle geocoding fallback for Lyon', () async {
      // Test du fallback pour Lyon
      final lyonCoords = await mapsService.getCoordinatesFromAddress('lyon');
      expect(lyonCoords, isNotNull);
      expect(lyonCoords!.latitude, equals(45.7640));
      expect(lyonCoords.longitude, equals(4.8357));
    });

    test('should handle geocoding fallback for Marseille', () async {
      // Test du fallback pour Marseille
      final marseilleCoords = await mapsService.getCoordinatesFromAddress('marseille');
      expect(marseilleCoords, isNotNull);
      expect(marseilleCoords!.latitude, equals(43.2965));
      expect(marseilleCoords.longitude, equals(5.3698));
    });

    test('should return null for invalid address', () async {
      final coords = await mapsService.getCoordinatesFromAddress('adresse_inexistante_12345');
      expect(coords, isNull);
    });

    test('should check navigation availability', () {
      expect(mapsService.isNavigationAvailable(), true);
    });

    test('should create marker correctly', () {
      final position = LatLng(48.8566, 2.3522);
      final marker = mapsService.createMarker(
        position: position,
        id: 'test_marker',
        title: 'Test Marker',
        snippet: 'Test snippet',
      );

      expect(marker.point, equals(position));
      expect(marker.width, equals(80));
      expect(marker.height, equals(80));
    });

    test('should handle state change callback', () {
      bool callbackCalled = false;
      
      mapsService.setStateChangeCallback(() {
        callbackCalled = true;
      });

      // Le callback devrait être défini
      expect(callbackCalled, false);
      
      // Note: Pour tester réellement le callback, il faudrait déclencher
      // une navigation, mais cela nécessite des mocks plus complexes
    });
  });
}

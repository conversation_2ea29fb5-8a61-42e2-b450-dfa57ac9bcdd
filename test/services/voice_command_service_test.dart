import 'package:flutter_test/flutter_test.dart';
import 'package:voice_assistant/services/voice_command_service.dart';

void main() {
  group('VoiceCommandService Tests', () {
    late VoiceCommandService voiceCommandService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      voiceCommandService = VoiceCommandService();
    });

    test('should detect translation commands', () async {
      // Test différents patterns de commandes de traduction
      final translationCommands = [
        'traduis en anglais : bonjour',
        'comment dit-on merci en espagnol',
        'traduis bonne nuit vers italien',
        'traduction de hello en français',
        'dis bonjour en allemand',
      ];

      for (final command in translationCommands) {
        // Note: Ce test vérifie seulement la détection, pas l'exécution
        // car cela nécessiterait une vraie clé API
        expect(
          command.toLowerCase(),
          contains(RegExp(r'traduis|comment dit-on|traduction|dis.*en')),
        );
      }
    });

    test('should not detect non-translation commands', () async {
      final nonTranslationCommands = [
        'quelle heure est-il',
        'rappelle-moi de faire les courses',
        'lance spotify',
        'quel temps fait-il',
      ];

      for (final command in nonTranslationCommands) {
        final containsTranslationKeywords =
            command.toLowerCase().contains('traduis') ||
            command.toLowerCase().contains('comment dit-on') ||
            command.toLowerCase().contains('traduction');

        expect(containsTranslationKeywords, false);
      }
    });

    test('should parse flexible translation commands', () {
      // Test de la logique de parsing (sans exécution réelle)
      final commands = [
        'traduis en anglais : bonjour comment allez-vous',
        'comment dit-on merci en espagnol',
        'dis hello en français',
      ];

      for (final command in commands) {
        // Vérifier que la commande contient les éléments nécessaires
        final hasLanguage =
            command.contains('anglais') ||
            command.contains('espagnol') ||
            command.contains('français');
        final hasText = command.length > 10; // Au moins quelques mots

        expect(hasLanguage, true);
        expect(hasText, true);
      }
    });
  });
}

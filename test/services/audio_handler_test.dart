import 'package:audio_service/audio_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:voice_assistant/services/audio_handler.dart';

@GenerateMocks([])
void main() {
  group('MyAudioHandler Tests', () {
    late MyAudioHandler audioHandler;

    setUp(() {
      audioHandler = MyAudioHandler();
    });

    test(
      'play() devrait mettre à jour l\'état de lecture à playing=true',
      () async {
        // État initial doit être non-playing
        expect(audioHandler.playbackState.value.playing, false);

        // Exécuter la méthode à tester
        await audioHandler.play();

        // Vérifier que l'état a changé
        expect(audioHandler.playbackState.value.playing, true);
        expect(
          audioHandler.playbackState.value.processingState,
          AudioProcessingState.ready,
        );
      },
    );

    test(
      'pause() devrait mettre à jour l\'état de lecture à playing=false',
      () async {
        // D'abord mettre en lecture
        await audioHandler.play();
        expect(audioHandler.playbackState.value.playing, true);

        // Exécuter la méthode à tester
        await audioHandler.pause();

        // Vérifier que l'état a changé
        expect(audioHandler.playbackState.value.playing, false);
      },
    );

    test(
      'stop() devrait mettre à jour l\'état de lecture à idle et playing=false',
      () async {
        // D'abord mettre en lecture
        await audioHandler.play();
        expect(audioHandler.playbackState.value.playing, true);

        // Exécuter la méthode à tester
        await audioHandler.stop();

        // Vérifier que l'état a changé
        expect(audioHandler.playbackState.value.playing, false);
        expect(
          audioHandler.playbackState.value.processingState,
          AudioProcessingState.idle,
        );
      },
    );

    test(
      'updatePlaybackState() devrait mettre à jour l\'état de lecture selon le paramètre',
      () async {
        // Test avec isPlaying=true
        audioHandler.updatePlaybackState(isPlaying: true);
        expect(audioHandler.playbackState.value.playing, true);
        expect(
          audioHandler.playbackState.value.processingState,
          AudioProcessingState.ready,
        );

        // Test avec isPlaying=false
        audioHandler.updatePlaybackState(isPlaying: false);
        expect(audioHandler.playbackState.value.playing, false);
        expect(
          audioHandler.playbackState.value.processingState,
          AudioProcessingState.idle,
        );
      },
    );
  });
}

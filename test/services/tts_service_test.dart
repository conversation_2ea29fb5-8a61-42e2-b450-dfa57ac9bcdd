import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/voice_detection_service.dart';

@GenerateNiceMocks([MockSpec<FlutterTts>(), MockSpec<VoiceDetectionService>()])
import 'tts_service_test.mocks.dart';

void main() {
  late MockFlutterTts mockFlutterTts;
  late MockVoiceDetectionService mockVoiceDetectionService;

  setUp(() {
    mockFlutterTts = MockFlutterTts();
    mockVoiceDetectionService = MockVoiceDetectionService();

    // Configuration des comportements par défaut des mocks
    when(mockFlutterTts.setLanguage(any)).thenAnswer((_) async => true);
    when(mockFlutterTts.setSpeechRate(any)).thenAnswer((_) async => true);
    when(mockFlutterTts.setVolume(any)).thenAnswer((_) async => true);
    when(mockFlutterTts.setPitch(any)).thenAnswer((_) async => true);
    when(
      mockFlutterTts.awaitSpeakCompletion(any),
    ).thenAnswer((_) async => true);
    when(mockFlutterTts.speak(any)).thenAnswer((_) async => 1);
    when(mockVoiceDetectionService.continuousListening).thenReturn(true);

    // Injection des mocks dans le service
    TtsService.setMocks(
      flutterTts: mockFlutterTts,
      voiceDetectionService: mockVoiceDetectionService,
    );
  });

  tearDown(() {
    TtsService.resetInstance();
  });

  group('TtsService', () {
    test('initialize configure correctement le service', () async {
      when(mockFlutterTts.setLanguage('fr-FR')).thenAnswer((_) async => true);
      when(mockFlutterTts.setSpeechRate(0.5)).thenAnswer((_) async => true);
      when(mockFlutterTts.setVolume(1.0)).thenAnswer((_) async => true);
      when(mockFlutterTts.setPitch(1.0)).thenAnswer((_) async => true);

      final ttsService = TtsService();
      await ttsService.initialize();

      verify(mockFlutterTts.setLanguage('fr-FR')).called(1);
      verify(mockFlutterTts.setSpeechRate(0.5)).called(1);
      verify(mockFlutterTts.setVolume(1.0)).called(1);
      verify(mockFlutterTts.setPitch(1.0)).called(1);
    });

    test('speak appelle correctement FlutterTts', () async {
      when(mockFlutterTts.speak(any)).thenAnswer((_) async => 1);

      final ttsService = TtsService();
      await ttsService.initialize();
      await ttsService.speak('Test message');

      verify(mockFlutterTts.speak('Test message')).called(1);
    });

    test('stop arrête correctement la synthèse vocale', () async {
      when(mockFlutterTts.stop()).thenAnswer((_) async => 1);

      final ttsService = TtsService();
      await ttsService.initialize();
      await ttsService.stop();

      verify(mockFlutterTts.stop()).called(1);
    });

    test('pause met correctement en pause la synthèse vocale', () async {
      when(mockFlutterTts.pause()).thenAnswer((_) async => 1);

      final ttsService = TtsService();
      await ttsService.initialize();
      await ttsService.pause();

      verify(mockFlutterTts.pause()).called(1);
    });

    group('Coordination avec le service de reconnaissance vocale', () {
      test('désactive la reconnaissance vocale pendant la synthèse', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        await ttsService.speak('Test message');

        verifyInOrder([
          mockVoiceDetectionService.stopListening(),
          mockFlutterTts.speak('Test message'),
        ]);
      });

      test('réactive la reconnaissance vocale après la synthèse', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        await ttsService.speak('Test message');
        // Simuler le délai après la synthèse
        await Future.delayed(const Duration(seconds: 1));

        verifyInOrder([
          mockVoiceDetectionService.stopListening(),
          mockFlutterTts.speak('Test message'),
          mockVoiceDetectionService.startContinuousListening(),
        ]);
      });

      test(
        'ne réactive pas la reconnaissance si la coordination est désactivée',
        () async {
          final ttsService = TtsService();
          await ttsService.initialize();
          ttsService.coordinateWithVoiceService = false;

          await ttsService.speak('Test message');
          await Future.delayed(const Duration(seconds: 1));

          verifyNever(mockVoiceDetectionService.stopListening());
          verifyNever(mockVoiceDetectionService.startContinuousListening());
        },
      );
    });

    group('Annonce de rappels', () {
      test('annonce correctement un rappel avec titre uniquement', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        await ttsService.announceReminder('Test reminder');

        verify(mockFlutterTts.speak('Rappel: Test reminder')).called(1);
      });

      test(
        'annonce correctement un rappel avec titre et description',
        () async {
          final ttsService = TtsService();
          await ttsService.initialize();

          await ttsService.announceReminder(
            'Test reminder',
            body: 'Test description',
          );

          verify(
            mockFlutterTts.speak('Rappel: Test reminder. Test description'),
          ).called(1);
        },
      );
    });

    group('Annonce des résultats de recherche', () {
      test('annonce correctement l\'absence de résultats', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        await ttsService.announceSearchResults([]);

        verify(
          mockFlutterTts.speak(
            "Je n'ai trouvé aucun résultat pour cette recherche.",
          ),
        ).called(1);
      });

      test('annonce correctement les résultats simples', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        await ttsService.announceSearchResults(['Résultat 1', 'Résultat 2']);

        verifyInOrder([
          mockFlutterTts.speak('Voici les résultats de votre recherche:'),
          mockFlutterTts.speak('Résultat 1'),
          mockFlutterTts.speak('Résultat 2'),
        ]);
      });

      test('annonce correctement les résultats structurés', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        final results = [
          {'title': 'Titre 1', 'description': 'Description 1'},
          {'title': 'Titre 2', 'description': 'Description 2'},
        ];

        await ttsService.announceSearchResults(results);

        verifyInOrder([
          mockFlutterTts.speak('Voici les résultats de votre recherche:'),
          mockFlutterTts.speak('Titre 1: Description 1'),
          mockFlutterTts.speak('Titre 2: Description 2'),
        ]);
      });

      test('limite le nombre de résultats annoncés à 3', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        final results = [
          'Résultat 1',
          'Résultat 2',
          'Résultat 3',
          'Résultat 4',
          'Résultat 5',
        ];

        await ttsService.announceSearchResults(results);

        verify(
          mockFlutterTts.speak('Voici les résultats de votre recherche:'),
        ).called(1);
        verify(mockFlutterTts.speak('Résultat 1')).called(1);
        verify(mockFlutterTts.speak('Résultat 2')).called(1);
        verify(mockFlutterTts.speak('Résultat 3')).called(1);
        verifyNever(mockFlutterTts.speak('Résultat 4'));
        verifyNever(mockFlutterTts.speak('Résultat 5'));
      });
    });

    group('Gestion des erreurs', () {
      test('gère correctement les erreurs d\'initialisation', () async {
        when(
          mockFlutterTts.setLanguage('fr-FR'),
        ).thenThrow(Exception('Erreur TTS'));

        final ttsService = TtsService();
        expect(() => ttsService.initialize(), throwsException);
      });

      test('gère correctement les erreurs de synthèse vocale', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        when(
          mockFlutterTts.speak(any),
        ).thenThrow(Exception('Erreur de synthèse'));

        expect(() => ttsService.speak('Test message'), throwsException);
      });

      test('continue à fonctionner après une erreur de synthèse', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        // Première tentative avec erreur
        when(
          mockFlutterTts.speak('Message erreur'),
        ).thenThrow(Exception('Erreur'));

        // La deuxième tentative devrait réussir
        when(mockFlutterTts.speak('Message succès')).thenAnswer((_) async => 1);

        expect(() => ttsService.speak('Message erreur'), throwsException);
        await ttsService.speak(
          'Message succès',
        ); // Ne devrait pas lever d'exception

        verify(mockFlutterTts.speak('Message succès')).called(1);
      });
    });

    group('Cas limites', () {
      test('gère correctement les chaînes vides', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        await ttsService.speak('');
        verifyNever(mockFlutterTts.speak(''));
      });

      test('gère correctement les valeurs null', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        // Utiliser une liste vide au lieu de null
        await ttsService.announceSearchResults([]);
        verify(
          mockFlutterTts.speak(
            "Je n'ai trouvé aucun résultat pour cette recherche.",
          ),
        ).called(1);
      });

      test('gère correctement les résultats de recherche malformés', () async {
        final ttsService = TtsService();
        await ttsService.initialize();

        final malformedResults = [
          {'titre_incorrect': 'Titre 1'},
          null,
          {'title': null, 'description': 'Description'},
        ];

        await ttsService.announceSearchResults(malformedResults);

        verify(
          mockFlutterTts.speak('Voici les résultats de votre recherche:'),
        ).called(1);
        verifyNever(mockFlutterTts.speak('null: null'));
      });
    });
  });
}

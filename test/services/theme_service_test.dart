import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:voice_assistant/services/theme_service.dart';

class MockThemeService extends Mock implements ThemeService {
  ThemeMode _themeMode = ThemeMode.system;
  Color _primaryColor = const Color(0xFF6C63FF);
  String _fontFamily = 'Poppins';

  @override
  ThemeMode get themeMode => _themeMode;

  @override
  Color get primaryColor => _primaryColor;

  @override
  String get fontFamily => _fontFamily;

  @override
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    notifyListeners();
  }

  @override
  Future<void> setPrimaryColor(Color color) async {
    _primaryColor = color;
    notifyListeners();
  }

  @override
  Future<void> setFontFamily(String family) async {
    _fontFamily = family;
    notifyListeners();
  }

  @override
  ThemeData getThemeData(BuildContext context) {
    final brightness =
        _themeMode == ThemeMode.dark ? Brightness.dark : Brightness.light;

    return ThemeData(
      brightness: brightness,
      primaryColor: _primaryColor,
      fontFamily: _fontFamily,
    );
  }

  final List<Function> _listeners = [];

  @override
  void addListener(Function listener) {
    _listeners.add(listener);
  }

  @override
  void removeListener(Function listener) {
    _listeners.remove(listener);
  }

  @override
  void notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }
}

@GenerateMocks([])
void main() {
  group('ThemeService Tests', () {
    late MockThemeService themeService;

    setUp(() async {
      SharedPreferences.setMockInitialValues({
        'theme_mode': ThemeMode.system.index,
        'primary_color': const Color(0xFF6C63FF).value,
        'font_family': 'Poppins',
      });

      themeService = MockThemeService();
    });

    test('devrait avoir les valeurs par défaut au démarrage', () {
      expect(themeService.themeMode, ThemeMode.system);
      expect(themeService.primaryColor, const Color(0xFF6C63FF));
      expect(themeService.fontFamily, 'Poppins');
    });

    test('setThemeMode devrait mettre à jour le mode de thème', () async {
      expect(themeService.themeMode, ThemeMode.system);

      await themeService.setThemeMode(ThemeMode.dark);
      expect(themeService.themeMode, ThemeMode.dark);

      await themeService.setThemeMode(ThemeMode.light);
      expect(themeService.themeMode, ThemeMode.light);
    });

    test('setPrimaryColor devrait mettre à jour la couleur primaire', () async {
      expect(themeService.primaryColor, const Color(0xFF6C63FF));

      const newColor = Color(0xFF00BCD4);
      await themeService.setPrimaryColor(newColor);
      expect(themeService.primaryColor, newColor);
    });

    test('setFontFamily devrait mettre à jour la famille de police', () async {
      expect(themeService.fontFamily, 'Poppins');

      await themeService.setFontFamily('Roboto');
      expect(themeService.fontFamily, 'Roboto');
    });

    testWidgets(
      'getThemeData devrait retourner un thème avec les bonnes propriétés',
      (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                final themeData = themeService.getThemeData(context);
                expect(themeData.primaryColor, const Color(0xFF6C63FF));
                return Container();
              },
            ),
          ),
        );
      },
    );

    testWidgets('getThemeData devrait respecter le mode sombre', (
      WidgetTester tester,
    ) async {
      await themeService.setThemeMode(ThemeMode.dark);

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final themeData = themeService.getThemeData(context);
              expect(themeData.brightness, Brightness.dark);
              return Container();
            },
          ),
        ),
      );
    });
  });
}

// Mock pour le BuildContext
class MockBuildContext extends Mock implements BuildContext {}

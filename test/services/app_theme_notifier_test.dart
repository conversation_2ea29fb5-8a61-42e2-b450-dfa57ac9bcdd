import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:voice_assistant/services/app_theme_notifier.dart';

class MockAppThemeNotifier extends Mock implements AppThemeNotifier {
  bool _isDarkMode;

  MockAppThemeNotifier(this._isDarkMode);

  @override
  bool get isDarkMode => _isDarkMode;

  @override
  Future<void> toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    notifyListeners();
  }

  @override
  Future<void> setDarkMode(bool value) async {
    _isDarkMode = value;
    notifyListeners();
  }

  final List<Function> _listeners = [];

  @override
  void addListener(Function listener) {
    _listeners.add(listener);
  }

  @override
  void removeListener(Function listener) {
    _listeners.remove(listener);
  }

  @override
  void notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }
}

@GenerateMocks([])
void main() {
  group('AppThemeNotifier Tests', () {
    late MockAppThemeNotifier themeNotifier;

    setUp(() async {
      SharedPreferences.setMockInitialValues({'darkMode': false});
    });

    test('devrait initialiser avec la valeur fournie', () async {
      // Utiliser le mock au lieu de la classe réelle
      themeNotifier = MockAppThemeNotifier(false);
      expect(themeNotifier.isDarkMode, false);

      final darkNotifier = MockAppThemeNotifier(true);
      expect(darkNotifier.isDarkMode, true);
    });

    test('toggleTheme() devrait inverser l\'état du mode sombre', () async {
      themeNotifier = MockAppThemeNotifier(false);
      expect(themeNotifier.isDarkMode, false);

      await themeNotifier.toggleTheme();
      expect(themeNotifier.isDarkMode, true);

      await themeNotifier.toggleTheme();
      expect(themeNotifier.isDarkMode, false);
    });

    test(
      'setDarkMode() devrait définir directement la valeur du mode sombre',
      () async {
        themeNotifier = MockAppThemeNotifier(false);
        expect(themeNotifier.isDarkMode, false);

        await themeNotifier.setDarkMode(true);
        expect(themeNotifier.isDarkMode, true);

        await themeNotifier.setDarkMode(false);
        expect(themeNotifier.isDarkMode, false);
      },
    );

    test(
      'les listeners devraient être notifiés lors des changements de thème',
      () async {
        themeNotifier = MockAppThemeNotifier(false);

        int notificationCount = 0;

        themeNotifier.addListener(() {
          notificationCount++;
        });

        await themeNotifier.toggleTheme();
        expect(notificationCount, 1);

        await themeNotifier.setDarkMode(false);
        expect(notificationCount, 2);
      },
    );
  });
}

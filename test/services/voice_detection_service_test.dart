import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_recognition_error.dart';
import 'package:voice_assistant/services/voice_detection_service.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';
import 'voice_detection_service_test.mocks.dart';

// Génération des mocks
@GenerateMocks([stt.SpeechToText])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late VoiceDetectionService voiceDetectionService;
  late MockSpeechToText mockSpeech;
  late Function(String) capturedStatusCallback;
  late Function(SpeechRecognitionResult) capturedResultCallback;

  setUp(() {
    mockSpeech = MockSpeechToText();

    // Configurer le mock des permissions
    final permissionHandler = TestPermissionHandler();
    PermissionHandlerPlatform.instance = permissionHandler;

    // Créer le service avec le mock
    voiceDetectionService = VoiceDetectionService(speechToText: mockSpeech);
    VoiceDetectionService.resetInstance();

    // Configuration plus détaillée des mocks
    when(
      mockSpeech.initialize(
        onStatus: anyNamed('onStatus'),
        onError: anyNamed('onError'),
      ),
    ).thenAnswer((invocation) {
      capturedStatusCallback =
          invocation.namedArguments[#onStatus] as Function(String);
      return Future.value(true);
    });

    when(
      mockSpeech.listen(
        onResult: anyNamed('onResult'),
        listenFor: anyNamed('listenFor'),
        pauseFor: anyNamed('pauseFor'),
        partialResults: anyNamed('partialResults'),
        localeId: anyNamed('localeId'),
        cancelOnError: anyNamed('cancelOnError'),
      ),
    ).thenAnswer((invocation) {
      capturedResultCallback =
          invocation.namedArguments[#onResult]
              as Function(SpeechRecognitionResult);
      return Future.value(true);
    });

    when(mockSpeech.stop()).thenAnswer((_) async => true);
    when(mockSpeech.isListening).thenReturn(false);
  });

  test('initialize should return true when permissions are granted', () async {
    final result = await voiceDetectionService.initialize();
    expect(result, true);
    verify(
      mockSpeech.initialize(
        onStatus: anyNamed('onStatus'),
        onError: anyNamed('onError'),
      ),
    ).called(1);
  });

  test(
    'startContinuousListening should start listening in real mode',
    () async {
      await voiceDetectionService.initialize();
      voiceDetectionService.startContinuousListening();

      verify(
        mockSpeech.listen(
          onResult: anyNamed('onResult'),
          listenFor: anyNamed('listenFor'),
          pauseFor: anyNamed('pauseFor'),
          partialResults: true,
          localeId: 'fr_FR',
          cancelOnError: false,
        ),
      ).called(1);
    },
  );

  test('stopListening should stop speech recognition', () async {
    await voiceDetectionService.initialize();
    when(mockSpeech.isListening).thenReturn(true);
    voiceDetectionService.stopListening();
    verify(mockSpeech.stop()).called(1);
  });

  test('state should change according to status callbacks', () async {
    await voiceDetectionService.initialize();

    capturedStatusCallback('listening');
    expect(voiceDetectionService.state, equals(VoiceState.listening));

    capturedStatusCallback('done');
    expect(voiceDetectionService.state, equals(VoiceState.idle));
  });

  test('should detect reminder read commands correctly', () async {
    await voiceDetectionService.initialize();
    bool wasReminderCommand = false;

    voiceDetectionService.commandDetected = (
      String command, {
      bool isReminderReadCommand = false,
    }) {
      wasReminderCommand = isReminderReadCommand;
    };

    voiceDetectionService.startContinuousListening();
    final result = SpeechRecognitionResult([
      SpeechRecognitionWords('lis mes rappels', 1.0),
    ], true);
    capturedResultCallback(result);

    expect(wasReminderCommand, true);
  });

  test('should handle continuous listening mode changes', () async {
    await voiceDetectionService.initialize();

    expect(voiceDetectionService.continuousListening, true);

    voiceDetectionService.continuousListening = false;
    expect(voiceDetectionService.continuousListening, false);
    verify(mockSpeech.stop()).called(1);

    voiceDetectionService.continuousListening = true;
    expect(voiceDetectionService.continuousListening, true);
    verify(
      mockSpeech.listen(
        onResult: anyNamed('onResult'),
        listenFor: anyNamed('listenFor'),
        pauseFor: anyNamed('pauseFor'),
        partialResults: true,
        localeId: 'fr_FR',
        cancelOnError: false,
      ),
    ).called(1);
  });

  test('should activate simulated mode when initialization fails', () async {
    // Simuler l'échec de l'initialisation
    when(
      mockSpeech.initialize(
        onStatus: anyNamed('onStatus'),
        onError: anyNamed('onError'),
      ),
    ).thenAnswer((_) => Future.value(false));

    final result = await voiceDetectionService.initialize();

    // Le service devrait quand même retourner true car il passe en mode simulé
    expect(result, true);
    expect(voiceDetectionService.recognizedText, isEmpty);
  });

  test('should handle errors properly', () async {
    await voiceDetectionService.initialize();

    // Simuler une erreur
    final errorCallback =
        verify(
              mockSpeech.initialize(
                onStatus: anyNamed('onStatus'),
                onError: captureAnyNamed('onError'),
              ),
            ).captured.first
            as Function;

    // Déclencher l'erreur
    errorCallback.call(SpeechRecognitionError('Test error', true));

    // Vérifier que l'état est passé en erreur
    expect(voiceDetectionService.state, equals(VoiceState.error));
    expect(voiceDetectionService.recognizedText, contains('Erreur'));
  });

  test('should restart listening automatically in continuous mode', () async {
    await voiceDetectionService.initialize();
    expect(voiceDetectionService.continuousListening, true);

    // Démarrer l'écoute initiale
    voiceDetectionService.startContinuousListening();

    // Simuler la fin d'une session d'écoute
    capturedStatusCallback('done');

    // Attendre un peu plus que le délai de redémarrage
    await Future.delayed(const Duration(milliseconds: 400));

    // Vérifier que l'écoute a redémarré
    verify(
      mockSpeech.listen(
        onResult: anyNamed('onResult'),
        listenFor: anyNamed('listenFor'),
        pauseFor: anyNamed('pauseFor'),
        partialResults: true,
        localeId: 'fr_FR',
        cancelOnError: false,
      ),
    ).called(
      2,
    ); // Une fois pour le démarrage initial, une fois pour le redémarrage
  });

  test(
    'should not restart listening when continuous mode is disabled',
    () async {
      await voiceDetectionService.initialize();
      clearInteractions(mockSpeech);

      // Démarrer l'écoute initiale
      voiceDetectionService.startContinuousListening();
      verify(
        mockSpeech.listen(
          onResult: anyNamed('onResult'),
          listenFor: anyNamed('listenFor'),
          pauseFor: anyNamed('pauseFor'),
          partialResults: anyNamed('partialResults'),
          localeId: anyNamed('localeId'),
          cancelOnError: anyNamed('cancelOnError'),
        ),
      ).called(1);
      clearInteractions(mockSpeech);

      // Désactiver le mode continu
      voiceDetectionService.continuousListening = false;
      verify(mockSpeech.stop()).called(1);
      clearInteractions(mockSpeech);

      // Simuler la fin d'une session d'écoute
      capturedStatusCallback('done');

      // Attendre un peu plus que le délai de redémarrage
      await Future.delayed(const Duration(milliseconds: 400));

      // Vérifier qu'il n'y a pas eu de nouvel appel à listen
      verifyNever(
        mockSpeech.listen(
          onResult: anyNamed('onResult'),
          listenFor: anyNamed('listenFor'),
          pauseFor: anyNamed('pauseFor'),
          partialResults: anyNamed('partialResults'),
          localeId: anyNamed('localeId'),
          cancelOnError: anyNamed('cancelOnError'),
        ),
      );
    },
  );

  group('reminder command detection', () {
    test('should detect various reminder read patterns', () async {
      await voiceDetectionService.initialize();
      final commands = [
        'lis mes rappels',
        'lire les rappels',
        'dit moi mes mémos',
        'dire les alarmes',
        'annonce mes rappels',
        'énoncer les mémos',
      ];

      for (final command in commands) {
        bool wasReminderCommand = false;
        voiceDetectionService.commandDetected = (
          String cmd, {
          bool isReminderReadCommand = false,
        }) {
          wasReminderCommand = isReminderReadCommand;
        };

        // Simuler le résultat de reconnaissance vocale
        when(
          mockSpeech.listen(
            onResult: anyNamed('onResult'),
            listenFor: anyNamed('listenFor'),
            pauseFor: anyNamed('pauseFor'),
            partialResults: anyNamed('partialResults'),
            localeId: anyNamed('localeId'),
            cancelOnError: anyNamed('cancelOnError'),
          ),
        ).thenAnswer((invocation) {
          final onResult = invocation.namedArguments[#onResult] as Function;
          onResult(
            SpeechRecognitionResult([
              SpeechRecognitionWords(command, 1.0),
            ], true),
          );
          return Future.value(true);
        });

        // Déclencher la reconnaissance
        voiceDetectionService.startContinuousListening();

        expect(
          wasReminderCommand,
          true,
          reason:
              'La commande "$command" devrait être détectée comme une commande de lecture de rappels',
        );
        clearInteractions(mockSpeech);
      }
    });

    test('should not detect non-reminder commands', () async {
      await voiceDetectionService.initialize();
      final commands = [
        'quelle heure est-il',
        'quel temps fait-il',
        'ouvre la fenêtre',
      ];

      for (final command in commands) {
        bool wasReminderCommand = false;
        voiceDetectionService.commandDetected = (
          String cmd, {
          bool isReminderReadCommand = false,
        }) {
          wasReminderCommand = isReminderReadCommand;
        };

        // Simuler le résultat de reconnaissance vocale
        when(
          mockSpeech.listen(
            onResult: anyNamed('onResult'),
            listenFor: anyNamed('listenFor'),
            pauseFor: anyNamed('pauseFor'),
            partialResults: anyNamed('partialResults'),
            localeId: anyNamed('localeId'),
            cancelOnError: anyNamed('cancelOnError'),
          ),
        ).thenAnswer((invocation) {
          final onResult = invocation.namedArguments[#onResult] as Function;
          onResult(
            SpeechRecognitionResult([
              SpeechRecognitionWords(command, 1.0),
            ], true),
          );
          return Future.value(true);
        });

        // Déclencher la reconnaissance
        voiceDetectionService.startContinuousListening();

        expect(
          wasReminderCommand,
          false,
          reason:
              'La commande "$command" ne devrait pas être détectée comme une commande de lecture de rappels',
        );
        clearInteractions(mockSpeech);
      }
    });
  });

  test('should handle denied permissions', () async {
    // Modifier le mock des permissions pour simuler un refus
    final permissionHandler =
        TestPermissionHandler()..shouldGrantPermission = false;
    PermissionHandlerPlatform.instance = permissionHandler;

    final result = await voiceDetectionService.initialize();

    // Le service devrait quand même retourner true car il passe en mode simulé
    expect(result, true);
    verifyNever(mockSpeech.initialize());
  });

  test('should cleanup resources on dispose', () async {
    await voiceDetectionService.initialize();
    voiceDetectionService.startContinuousListening();

    // Simuler que le service est en train d'écouter
    when(mockSpeech.isListening).thenReturn(true);

    // Appeler dispose
    voiceDetectionService.dispose();

    // Vérifier que l'écoute a été arrêtée
    verify(mockSpeech.stop()).called(1);
  });
}

// Mock pour les permissions
class TestPermissionHandler extends PermissionHandlerPlatform {
  bool shouldGrantPermission = true;

  @override
  Future<PermissionStatus> request(Permission permission) async {
    return shouldGrantPermission
        ? PermissionStatus.granted
        : PermissionStatus.denied;
  }

  @override
  Future<bool> openAppSettings() async {
    return true;
  }

  @override
  Future<ServiceStatus> checkServiceStatus(Permission permission) async {
    return ServiceStatus.enabled;
  }

  @override
  Future<PermissionStatus> checkPermissionStatus(Permission permission) async {
    return shouldGrantPermission
        ? PermissionStatus.granted
        : PermissionStatus.denied;
  }

  @override
  Future<Map<Permission, PermissionStatus>> requestPermissions(
    List<Permission> permissions,
  ) async {
    return Map.fromIterable(
      permissions,
      key: (permission) => permission,
      value:
          (_) =>
              shouldGrantPermission
                  ? PermissionStatus.granted
                  : PermissionStatus.denied,
    );
  }

  @override
  Future<bool> shouldShowRequestPermissionRationale(
    Permission permission,
  ) async {
    return false;
  }
}

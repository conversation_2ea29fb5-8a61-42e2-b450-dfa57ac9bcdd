import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:voice_assistant/services/auth_service.dart';

// Générer le mock pour LocalAuthentication
@GenerateMocks([LocalAuthentication])
import 'auth_service_test.mocks.dart';

void main() {
  late AuthService authService;
  late MockLocalAuthentication mockLocalAuth;

  setUp(() {
    mockLocalAuth = MockLocalAuthentication();
    authService = AuthService(auth: mockLocalAuth);
  });

  group('Vérification de disponibilité biométrique', () {
    test('retourne true quand la biométrie est disponible', () async {
      when(mockLocalAuth.canCheckBiometrics).thenAnswer((_) async => true);
      when(mockLocalAuth.isDeviceSupported()).thenAnswer((_) async => true);
      when(mockLocalAuth.getAvailableBiometrics()).thenAnswer(
        (_) async => [BiometricType.fingerprint, BiometricType.face],
      );

      final result = await authService.checkBiometricAvailability();

      expect(result, true);
      verify(mockLocalAuth.canCheckBiometrics).called(1);
      verify(mockLocalAuth.isDeviceSupported()).called(1);
      verify(mockLocalAuth.getAvailableBiometrics()).called(1);
    });

    test(
      'retourne false si l\'appareil ne supporte pas la biométrie',
      () async {
        when(mockLocalAuth.canCheckBiometrics).thenAnswer((_) async => true);
        when(mockLocalAuth.isDeviceSupported()).thenAnswer((_) async => false);

        final result = await authService.checkBiometricAvailability();

        expect(result, false);
        verify(mockLocalAuth.canCheckBiometrics).called(1);
        verify(mockLocalAuth.isDeviceSupported()).called(1);
        verifyNever(mockLocalAuth.getAvailableBiometrics());
      },
    );
  });

  group('Gestion des types biométriques', () {
    test('récupère correctement les types biométriques disponibles', () async {
      // Configurer le mock pour simuler les disponibilités biométriques
      when(mockLocalAuth.canCheckBiometrics).thenAnswer((_) async => true);
      when(mockLocalAuth.isDeviceSupported()).thenAnswer((_) async => true);
      when(mockLocalAuth.getAvailableBiometrics()).thenAnswer(
        (_) async => [BiometricType.fingerprint, BiometricType.face],
      );

      final biometrics = await authService.getAvailableBiometrics();

      expect(
        biometrics,
        containsAll([BiometricType.fingerprint, BiometricType.face]),
      );
      verify(mockLocalAuth.getAvailableBiometrics()).called(1);
    });

    test('gère le cas où aucune biométrie n\'est disponible', () async {
      when(mockLocalAuth.canCheckBiometrics).thenAnswer((_) async => false);
      when(mockLocalAuth.isDeviceSupported()).thenAnswer((_) async => true);
      when(mockLocalAuth.getAvailableBiometrics()).thenAnswer((_) async => []);

      final biometrics = await authService.getAvailableBiometrics();

      expect(biometrics, isEmpty);
    });

    test('met en cache les types biométriques disponibles', () async {
      when(mockLocalAuth.canCheckBiometrics).thenAnswer((_) async => true);
      when(mockLocalAuth.isDeviceSupported()).thenAnswer((_) async => true);
      when(
        mockLocalAuth.getAvailableBiometrics(),
      ).thenAnswer((_) async => [BiometricType.fingerprint]);

      // Premier appel devrait vérifier la disponibilité
      await authService.getAvailableBiometrics();
      // Deuxième appel devrait utiliser le cache
      await authService.getAvailableBiometrics();

      // getAvailableBiometrics ne devrait être appelé qu'une seule fois
      verify(mockLocalAuth.getAvailableBiometrics()).called(1);
    });
  });

  group('Gestion des tentatives d\'authentification', () {
    test('bloque après trois tentatives échouées', () async {
      // Configurer le mock pour simuler des échecs d'authentification
      when(
        mockLocalAuth.authenticate(
          localizedReason: anyNamed('localizedReason'),
          options: anyNamed('options'),
        ),
      ).thenAnswer((_) async => false);

      // Première tentative
      await authService.authenticateWithBiometrics();
      expect(authService.remainingAttempts, equals(2));
      expect(authService.isBlocked, isFalse);

      // Deuxième tentative
      await authService.authenticateWithBiometrics();
      expect(authService.remainingAttempts, equals(1));
      expect(authService.isBlocked, isFalse);

      // Troisième tentative
      await authService.authenticateWithBiometrics();
      expect(authService.remainingAttempts, equals(0));
      expect(authService.isBlocked, isTrue);

      // Quatrième tentative devrait échouer
      expect(
        () => authService.authenticateWithBiometrics(),
        throwsA(isA<AuthenticationException>()),
      );
    });

    test(
      'réinitialise le compteur après une authentification réussie',
      () async {
        // Configurer d'abord des échecs
        when(
          mockLocalAuth.authenticate(
            localizedReason: anyNamed('localizedReason'),
            options: anyNamed('options'),
          ),
        ).thenAnswer((_) async => false);

        // Deux tentatives échouées
        await authService.authenticateWithBiometrics();
        await authService.authenticateWithBiometrics();
        expect(authService.remainingAttempts, equals(1));

        // Reconfigurer le mock pour un succès
        reset(mockLocalAuth);
        when(
          mockLocalAuth.authenticate(
            localizedReason: anyNamed('localizedReason'),
            options: anyNamed('options'),
          ),
        ).thenAnswer((_) async => true);

        // Authentification réussie
        await authService.authenticateWithBiometrics();
        expect(authService.remainingAttempts, equals(3));
        expect(authService.isAuthenticated, isTrue);
      },
    );

    test('réinitialise manuellement le compteur de tentatives', () async {
      when(
        mockLocalAuth.authenticate(
          localizedReason: anyNamed('localizedReason'),
          options: anyNamed('options'),
        ),
      ).thenAnswer((_) async => false);

      // Deux tentatives échouées
      await authService.authenticateWithBiometrics();
      await authService.authenticateWithBiometrics();
      expect(authService.remainingAttempts, equals(1));

      // Réinitialisation manuelle
      authService.resetFailedAttempts();
      expect(authService.remainingAttempts, equals(3));
      expect(authService.isBlocked, isFalse);
    });
  });

  group('Authentification biométrique', () {
    test('authentifie avec succès via biométrie', () async {
      // Configurer le mock pour simuler une authentification réussie
      when(
        mockLocalAuth.authenticate(
          localizedReason: anyNamed('localizedReason'),
          options: anyNamed('options'),
        ),
      ).thenAnswer((_) async => true);

      final result = await authService.authenticateWithBiometrics();

      expect(result, true);
      expect(authService.isAuthenticated, true);

      // Vérifier que la méthode a été appelée avec les bons paramètres
      verify(
        mockLocalAuth.authenticate(
          localizedReason: anyNamed('localizedReason'),
          options: anyNamed('options'),
        ),
      ).called(1);
    });

    test('gère l\'échec de l\'authentification biométrique', () async {
      when(
        mockLocalAuth.authenticate(
          localizedReason: anyNamed('localizedReason'),
          options: anyNamed('options'),
        ),
      ).thenAnswer((_) async => false);

      final result = await authService.authenticateWithBiometrics();

      expect(result, false);
      expect(authService.isAuthenticated, false);
    });

    test('gère les erreurs d\'authentification', () async {
      // Configurer le mock pour simuler une exception
      when(
        mockLocalAuth.authenticate(
          localizedReason: anyNamed('localizedReason'),
          options: anyNamed('options'),
        ),
      ).thenThrow(Exception('Erreur d\'authentification'));

      // L'appel à authenticateWithBiometrics devrait capturer et transformer l'exception
      expect(
        () => authService.authenticateWithBiometrics(),
        throwsA(isA<AuthenticationException>()),
      );
    });

    test('annule l\'authentification en cours', () async {
      // Configurer le mock pour le test d'annulation
      when(mockLocalAuth.stopAuthentication()).thenAnswer((_) async => true);

      // Exécuter la méthode à tester
      await authService.cancelAuthentication();

      // Vérifier que la méthode a été appelée
      verify(mockLocalAuth.stopAuthentication()).called(1);

      // Vérifier que l'état d'authentification est mis à jour
      expect(authService.isAuthenticated, false);
    });

    test('gère la déconnexion', () async {
      // Configurer une authentification réussie
      when(
        mockLocalAuth.authenticate(
          localizedReason: anyNamed('localizedReason'),
          options: anyNamed('options'),
        ),
      ).thenAnswer((_) async => true);

      // Authentifier
      final result = await authService.authenticateWithBiometrics();
      expect(result, true);
      expect(authService.isAuthenticated, true);

      // Déconnexion
      authService.logout();
      expect(authService.isAuthenticated, false);
    });
  });
}

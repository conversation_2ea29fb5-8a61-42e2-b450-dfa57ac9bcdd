import 'package:flutter_test/flutter_test.dart';
import 'package:voice_assistant/services/yolo_detector.dart';

void main() {
  group('YoloDetector', () {
    late YoloDetector detector;

    setUp(() {
      detector = YoloDetector();
    });

    test('should initialize detector', () {
      expect(detector.isInitialized, false);
    });

    test('should handle initialization failure gracefully', () async {
      // Le test ne peut pas charger le modèle réel en mode test
      // mais il ne devrait pas planter
      expect(() => detector.initialize(), returnsNormally);
    });

    tearDown(() {
      detector.dispose();
    });
  });
}

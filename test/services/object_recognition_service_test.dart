import 'package:flutter_test/flutter_test.dart';
import 'package:voice_assistant/services/object_recognition_service.dart';

void main() {
  group('ObjectRecognitionService', () {
    late ObjectRecognitionService service;

    setUp(() {
      service = ObjectRecognitionService();
    });

    test('should initialize service', () async {
      expect(service.isInitialized, false);
      expect(service.isDetecting, false);
      expect(service.isModelLoaded, false);
    });

    test('should create DetectedObject from map', () {
      final map = {
        'class': 'person',
        'confidence': 0.85,
        'x': 0.1,
        'y': 0.2,
        'width': 0.3,
        'height': 0.4,
      };

      final obj = DetectedObject.fromMap(map);

      expect(obj.className, 'person');
      expect(obj.confidence, 0.85);
      expect(obj.x, 0.1);
      expect(obj.y, 0.2);
      expect(obj.width, 0.3);
      expect(obj.height, 0.4);
    });

    test('should translate class names to French', () {
      final person = DetectedObject(
        className: 'person',
        confidence: 0.9,
        x: 0.0,
        y: 0.0,
        width: 0.1,
        height: 0.1,
      );

      final car = DetectedObject(
        className: 'car',
        confidence: 0.8,
        x: 0.0,
        y: 0.0,
        width: 0.1,
        height: 0.1,
      );

      final unknown = DetectedObject(
        className: 'unknown_object',
        confidence: 0.7,
        x: 0.0,
        y: 0.0,
        width: 0.1,
        height: 0.1,
      );

      expect(person.classNameFr, 'Personne');
      expect(car.classNameFr, 'Voiture');
      expect(unknown.classNameFr, 'unknown_object'); // Pas de traduction
    });

    test('should format confidence percentage', () {
      final obj = DetectedObject(
        className: 'test',
        confidence: 0.8567,
        x: 0.0,
        y: 0.0,
        width: 0.1,
        height: 0.1,
      );

      expect(obj.confidencePercentage, '85.7%');
    });
  });
}

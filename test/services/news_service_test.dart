import 'package:flutter_test/flutter_test.dart';
import 'package:voice_assistant/services/news_service.dart';

void main() {
  group('NewsService Tests', () {
    late NewsService newsService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      newsService = NewsService();
    });

    test('should support common categories', () {
      expect(newsService.isCategorySupported('technologie'), true);
      expect(newsService.isCategorySupported('sport'), true);
      expect(newsService.isCategorySupported('business'), true);
      expect(newsService.isCategorySupported('tech'), true);
      expect(newsService.isCategorySupported('xyz'), false);
    });

    test('should get supported categories list', () {
      final categories = newsService.getSupportedCategories();
      expect(categories, isNotEmpty);
      expect(categories.contains('technologie'), true);
      expect(categories.contains('sport'), true);
    });

    test('should create NewsArticle from JSON', () {
      final json = {
        'title': 'Test Article',
        'description': 'Test Description',
        'content': 'Test Content',
        'url': 'https://example.com',
        'urlToImage': 'https://example.com/image.jpg',
        'publishedAt': '2024-01-01T12:00:00Z',
        'source': {'name': 'Test Source'},
        'author': 'Test Author',
      };

      final article = NewsArticle.fromJson(json);
      
      expect(article.title, 'Test Article');
      expect(article.description, 'Test Description');
      expect(article.content, 'Test Content');
      expect(article.url, 'https://example.com');
      expect(article.urlToImage, 'https://example.com/image.jpg');
      expect(article.sourceName, 'Test Source');
      expect(article.author, 'Test Author');
    });

    test('should handle missing fields in JSON', () {
      final json = <String, dynamic>{};

      final article = NewsArticle.fromJson(json);
      
      expect(article.title, '');
      expect(article.description, '');
      expect(article.content, '');
      expect(article.url, '');
      expect(article.urlToImage, null);
      expect(article.sourceName, 'Source inconnue');
      expect(article.author, null);
    });

    test('should get short summary', () {
      final article = NewsArticle(
        title: 'Test Title',
        description: 'This is a very long description that should be truncated when getting a short summary because it exceeds the maximum length allowed for short summaries',
        content: 'Test Content',
        url: 'https://example.com',
        publishedAt: '2024-01-01T12:00:00Z',
        sourceName: 'Test Source',
      );

      final shortSummary = article.getShortSummary();
      expect(shortSummary.length, lessThanOrEqualTo(103)); // 100 + "..."
      expect(shortSummary.endsWith('...'), true);
    });

    test('should format date correctly', () {
      final now = DateTime.now();
      final oneHourAgo = now.subtract(const Duration(hours: 1));
      
      final article = NewsArticle(
        title: 'Test Title',
        description: 'Test Description',
        content: 'Test Content',
        url: 'https://example.com',
        publishedAt: oneHourAgo.toIso8601String(),
        sourceName: 'Test Source',
      );

      final formattedDate = article.getFormattedDate();
      expect(formattedDate, contains('heure'));
    });

    // Note: Les tests suivants nécessitent une vraie clé API
    // et une connexion internet, donc ils sont commentés pour les tests unitaires
    
    /*
    test('should fetch news by category', () async {
      final articles = await newsService.getNewsByCategory(
        category: 'technology',
        pageSize: 5,
      );
      
      expect(articles, isNotNull);
      expect(articles!.length, lessThanOrEqualTo(5));
      
      for (final article in articles) {
        expect(article.title, isNotEmpty);
        expect(article.sourceName, isNotEmpty);
      }
    });

    test('should search news', () async {
      final articles = await newsService.searchNews(
        query: 'technology',
        pageSize: 3,
      );
      
      expect(articles, isNotNull);
      expect(articles!.length, lessThanOrEqualTo(3));
    });
    */
  });
}

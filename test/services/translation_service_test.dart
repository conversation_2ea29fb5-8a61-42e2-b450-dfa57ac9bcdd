import 'package:flutter_test/flutter_test.dart';
import 'package:voice_assistant/services/translation_service.dart';

void main() {
  group('TranslationService Tests', () {
    late TranslationService translationService;

    setUp(() {
      translationService = TranslationService();
    });

    test('should support common languages', () {
      expect(translationService.isLanguageSupported('français'), true);
      expect(translationService.isLanguageSupported('anglais'), true);
      expect(translationService.isLanguageSupported('espagnol'), true);
      expect(translationService.isLanguageSupported('en'), true);
      expect(translationService.isLanguageSupported('fr'), true);
      expect(translationService.isLanguageSupported('es'), true);
      expect(translationService.isLanguageSupported('xyz'), false);
    });

    test('should get language code correctly', () {
      expect(translationService.isLanguageSupported('français'), true);
      expect(translationService.isLanguageSupported('fr'), true);
    });

    test('should get supported languages list', () {
      final languages = translationService.getSupportedLanguages();
      expect(languages, isNotEmpty);
      expect(languages.contains('français'), true);
      expect(languages.contains('anglais'), true);
    });

    test('should get supported language codes list', () {
      final codes = translationService.getSupportedLanguageCodes();
      expect(codes, isNotEmpty);
      expect(codes.contains('fr'), true);
      expect(codes.contains('en'), true);
    });

    // Note: Les tests suivants nécessitent une vraie clé API
    // et une connexion internet, donc ils sont commentés pour les tests unitaires
    
    /*
    test('should translate text successfully', () async {
      final result = await translationService.translateText(
        text: 'Bonjour',
        targetLanguage: 'en',
      );
      
      expect(result, isNotNull);
      expect(result!.originalText, 'Bonjour');
      expect(result.translatedText.toLowerCase(), contains('hello'));
      expect(result.targetLanguage, 'en');
    });

    test('should detect language', () async {
      final language = await translationService.detectLanguage('Hello world');
      expect(language, 'en');
    });
    */
  });
}

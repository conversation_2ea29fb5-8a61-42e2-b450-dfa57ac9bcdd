import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/voice_detection_service.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:speech_to_text/speech_to_text.dart';

@GenerateMocks([
  FirebaseAuth,
  UserCredential,
  User,
  TtsService,
  VoiceDetectionService,
  FlutterTts,
  SpeechToText,
])
void main() {}

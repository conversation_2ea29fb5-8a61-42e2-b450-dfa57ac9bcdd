import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/pages/object_recognition_page.dart';
import 'package:voice_assistant/services/theme_service.dart';

void main() {
  group('ObjectRecognitionPage', () {
    testWidgets('should display permission request initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider<ThemeService>(
                create: (_) => ThemeService(),
              ),
            ],
            child: const ObjectRecognitionPage(),
          ),
        ),
      );

      // Attendre que l'interface se charge
      await tester.pump();

      // Vérifier que la page s'affiche
      expect(find.byType(ObjectRecognitionPage), findsOneWidget);

      // Vérifier le titre de l'AppBar
      expect(find.text('Reconnaissance d\'Objets'), findsOneWidget);
    });

    testWidgets('should have proper app bar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider<ThemeService>(
                create: (_) => ThemeService(),
              ),
            ],
            child: const ObjectRecognitionPage(),
          ),
        ),
      );

      await tester.pump();

      // Vérifier l'AppBar
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Reconnaissance d\'Objets'), findsOneWidget);
    });

    testWidgets('should display loading state', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider<ThemeService>(
                create: (_) => ThemeService(),
              ),
            ],
            child: const ObjectRecognitionPage(),
          ),
        ),
      );

      // Attendre l'état initial
      await tester.pump();

      // Vérifier qu'il y a un indicateur de chargement ou un message d'état
      expect(
        find.byType(CircularProgressIndicator),
        findsWidgets,
      );
    });

    testWidgets('should have voice control buttons when initialized', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider<ThemeService>(
                create: (_) => ThemeService(),
              ),
            ],
            child: const ObjectRecognitionPage(),
          ),
        ),
      );

      await tester.pump();

      // Les boutons de contrôle vocal devraient être présents une fois initialisé
      // (En mode test, ils ne seront pas visibles car le service n'est pas initialisé)
      expect(find.byType(AppBar), findsOneWidget);
    });
  });
}

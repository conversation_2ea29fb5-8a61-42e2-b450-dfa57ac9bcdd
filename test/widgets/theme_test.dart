import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/services/app_theme_notifier.dart';
import 'package:mockito/mockito.dart';

class MockAppThemeNotifier extends Mock implements AppThemeNotifier {
  bool _isDarkMode;

  MockAppThemeNotifier(this._isDarkMode);

  @override
  bool get isDarkMode => _isDarkMode;

  @override
  Future<void> toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    notifyListeners();
  }

  final List<Function> _listeners = [];

  @override
  void addListener(Function listener) {
    _listeners.add(listener);
  }

  @override
  void removeListener(Function listener) {
    _listeners.remove(listener);
  }

  @override
  void notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }
}

void main() {
  group('Theme Tests', () {
    testWidgets('Le thème devrait changer quand toggleTheme est appelé', (
      WidgetTester tester,
    ) async {
      // Arrange - Utiliser le Mock au lieu de la classe réelle
      final themeNotifier = MockAppThemeNotifier(false);

      await tester.pumpWidget(
        ChangeNotifierProvider<AppThemeNotifier>.value(
          value: themeNotifier,
          child: MaterialApp(
            home: Builder(
              builder: (context) {
                final isDark =
                    Provider.of<AppThemeNotifier>(context).isDarkMode;
                return Scaffold(
                  body: Text(isDark ? 'Dark Mode' : 'Light Mode'),
                );
              },
            ),
          ),
        ),
      );

      // Verify initial state
      expect(find.text('Light Mode'), findsOneWidget);
      expect(find.text('Dark Mode'), findsNothing);

      // Act
      await themeNotifier.toggleTheme();
      await tester.pump();

      // Assert
      expect(find.text('Dark Mode'), findsOneWidget);
      expect(find.text('Light Mode'), findsNothing);
    });

    testWidgets('Le thème devrait persister après un redémarrage', (
      WidgetTester tester,
    ) async {
      // Arrange
      final themeNotifier = MockAppThemeNotifier(true);

      await tester.pumpWidget(
        ChangeNotifierProvider<AppThemeNotifier>.value(
          value: themeNotifier,
          child: MaterialApp(
            home: Builder(
              builder: (context) {
                final isDark =
                    Provider.of<AppThemeNotifier>(context).isDarkMode;
                return Scaffold(
                  body: Text(isDark ? 'Dark Mode' : 'Light Mode'),
                );
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Dark Mode'), findsOneWidget);
      expect(find.text('Light Mode'), findsNothing);
    });
  });
}

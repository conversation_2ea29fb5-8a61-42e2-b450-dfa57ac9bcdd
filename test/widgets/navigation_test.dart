import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:voice_assistant/pages/home_page.dart';
import 'package:voice_assistant/pages/login_page.dart';
import 'package:voice_assistant/pages/maps_page.dart';
import 'package:voice_assistant/pages/test_speech_page.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/services/app_theme_notifier.dart';

void main() {
  group('Navigation Tests', () {
    testWidgets('La navigation devrait fonctionner correctement', (
      WidgetTester tester,
    ) async {
      // Arrange
      await tester.pumpWidget(
        ChangeNotifierProvider<AppThemeNotifier>(
          create: (_) => AppThemeNotifier(false),
          child: MaterialApp(
            initialRoute: '/',
            routes: {
              '/': (context) => const LoginPage(),
              '/home': (context) => const HomePage(),
              '/test_speech': (context) => const TestSpeechPage(),
              '/maps': (context) => const MapsPage(),
            },
          ),
        ),
      );

      // Verify initial route
      expect(find.byType(LoginPage), findsOneWidget);

      // Navigate to home
      await tester.tap(find.byType(ElevatedButton).first);
      await tester.pumpAndSettle();

      // Verify navigation to home
      expect(find.byType(HomePage), findsOneWidget);

      // Navigate to test speech
      await tester.tap(find.byIcon(Icons.mic));
      await tester.pumpAndSettle();

      // Verify navigation to test speech
      expect(find.byType(TestSpeechPage), findsOneWidget);

      // Navigate back
      await tester.tap(find.byType(BackButton));
      await tester.pumpAndSettle();

      // Verify back navigation
      expect(find.byType(HomePage), findsOneWidget);
    });

    testWidgets('Le drawer devrait s\'ouvrir et se fermer correctement', (
      WidgetTester tester,
    ) async {
      // Arrange
      await tester.pumpWidget(
        ChangeNotifierProvider<AppThemeNotifier>(
          create: (_) => AppThemeNotifier(false),
          child: MaterialApp(home: const HomePage()),
        ),
      );

      // Verify drawer is closed
      expect(find.byType(Drawer), findsNothing);

      // Open drawer
      await tester.tap(find.byType(IconButton).first);
      await tester.pumpAndSettle();

      // Verify drawer is open
      expect(find.byType(Drawer), findsOneWidget);

      // Close drawer by tapping outside
      await tester.tapAt(const Offset(500, 500));
      await tester.pumpAndSettle();

      // Verify drawer is closed
      expect(find.byType(Drawer), findsNothing);
    });
  });
}

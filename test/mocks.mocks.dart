// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in voice_assistant/test/mocks.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:ui' as _i10;

import 'package:firebase_auth/firebase_auth.dart' as _i4;
import 'package:firebase_auth_platform_interface/firebase_auth_platform_interface.dart'
    as _i3;
import 'package:firebase_core/firebase_core.dart' as _i2;
import 'package:flutter/services.dart' as _i11;
import 'package:flutter_tts/flutter_tts.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;
import 'package:speech_to_text/speech_to_text.dart' as _i12;
import 'package:voice_assistant/services/tts_service.dart' as _i8;
import 'package:voice_assistant/services/voice_detection_service.dart' as _i9;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseApp_0 extends _i1.SmartFake implements _i2.FirebaseApp {
  _FakeFirebaseApp_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeActionCodeInfo_1 extends _i1.SmartFake
    implements _i3.ActionCodeInfo {
  _FakeActionCodeInfo_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserCredential_2 extends _i1.SmartFake
    implements _i4.UserCredential {
  _FakeUserCredential_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeConfirmationResult_3 extends _i1.SmartFake
    implements _i4.ConfirmationResult {
  _FakeConfirmationResult_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserMetadata_4 extends _i1.SmartFake implements _i3.UserMetadata {
  _FakeUserMetadata_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMultiFactor_5 extends _i1.SmartFake implements _i4.MultiFactor {
  _FakeMultiFactor_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIdTokenResult_6 extends _i1.SmartFake implements _i3.IdTokenResult {
  _FakeIdTokenResult_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUser_7 extends _i1.SmartFake implements _i4.User {
  _FakeUser_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSpeechRateValidRange_8 extends _i1.SmartFake
    implements _i5.SpeechRateValidRange {
  _FakeSpeechRateValidRange_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FirebaseAuth].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAuth extends _i1.Mock implements _i4.FirebaseAuth {
  MockFirebaseAuth() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseApp get app =>
      (super.noSuchMethod(
            Invocation.getter(#app),
            returnValue: _FakeFirebaseApp_0(this, Invocation.getter(#app)),
          )
          as _i2.FirebaseApp);

  @override
  set app(_i2.FirebaseApp? _app) => super.noSuchMethod(
    Invocation.setter(#app, _app),
    returnValueForMissingStub: null,
  );

  @override
  set tenantId(String? tenantId) => super.noSuchMethod(
    Invocation.setter(#tenantId, tenantId),
    returnValueForMissingStub: null,
  );

  @override
  Map<dynamic, dynamic> get pluginConstants =>
      (super.noSuchMethod(
            Invocation.getter(#pluginConstants),
            returnValue: <dynamic, dynamic>{},
          )
          as Map<dynamic, dynamic>);

  @override
  _i6.Future<void> useEmulator(String? origin) =>
      (super.noSuchMethod(
            Invocation.method(#useEmulator, [origin]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> useAuthEmulator(
    String? host,
    int? port, {
    bool? automaticHostMapping = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #useAuthEmulator,
              [host, port],
              {#automaticHostMapping: automaticHostMapping},
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> applyActionCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#applyActionCode, [code]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i3.ActionCodeInfo> checkActionCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#checkActionCode, [code]),
            returnValue: _i6.Future<_i3.ActionCodeInfo>.value(
              _FakeActionCodeInfo_1(
                this,
                Invocation.method(#checkActionCode, [code]),
              ),
            ),
          )
          as _i6.Future<_i3.ActionCodeInfo>);

  @override
  _i6.Future<void> confirmPasswordReset({
    required String? code,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#confirmPasswordReset, [], {
              #code: code,
              #newPassword: newPassword,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.UserCredential> createUserWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createUserWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#createUserWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<List<String>> fetchSignInMethodsForEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#fetchSignInMethodsForEmail, [email]),
            returnValue: _i6.Future<List<String>>.value(<String>[]),
          )
          as _i6.Future<List<String>>);

  @override
  _i6.Future<_i4.UserCredential> getRedirectResult() =>
      (super.noSuchMethod(
            Invocation.method(#getRedirectResult, []),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#getRedirectResult, []),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  bool isSignInWithEmailLink(String? emailLink) =>
      (super.noSuchMethod(
            Invocation.method(#isSignInWithEmailLink, [emailLink]),
            returnValue: false,
          )
          as bool);

  @override
  _i6.Stream<_i4.User?> authStateChanges() =>
      (super.noSuchMethod(
            Invocation.method(#authStateChanges, []),
            returnValue: _i6.Stream<_i4.User?>.empty(),
          )
          as _i6.Stream<_i4.User?>);

  @override
  _i6.Stream<_i4.User?> idTokenChanges() =>
      (super.noSuchMethod(
            Invocation.method(#idTokenChanges, []),
            returnValue: _i6.Stream<_i4.User?>.empty(),
          )
          as _i6.Stream<_i4.User?>);

  @override
  _i6.Stream<_i4.User?> userChanges() =>
      (super.noSuchMethod(
            Invocation.method(#userChanges, []),
            returnValue: _i6.Stream<_i4.User?>.empty(),
          )
          as _i6.Stream<_i4.User?>);

  @override
  _i6.Future<void> sendPasswordResetEmail({
    required String? email,
    _i3.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {
              #email: email,
              #actionCodeSettings: actionCodeSettings,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendSignInLinkToEmail({
    required String? email,
    required _i3.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSignInLinkToEmail, [], {
              #email: email,
              #actionCodeSettings: actionCodeSettings,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setLanguageCode(String? languageCode) =>
      (super.noSuchMethod(
            Invocation.method(#setLanguageCode, [languageCode]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setSettings({
    bool? appVerificationDisabledForTesting = false,
    String? userAccessGroup,
    String? phoneNumber,
    String? smsCode,
    bool? forceRecaptchaFlow,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setSettings, [], {
              #appVerificationDisabledForTesting:
                  appVerificationDisabledForTesting,
              #userAccessGroup: userAccessGroup,
              #phoneNumber: phoneNumber,
              #smsCode: smsCode,
              #forceRecaptchaFlow: forceRecaptchaFlow,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setPersistence(_i3.Persistence? persistence) =>
      (super.noSuchMethod(
            Invocation.method(#setPersistence, [persistence]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.UserCredential> signInAnonymously() =>
      (super.noSuchMethod(
            Invocation.method(#signInAnonymously, []),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#signInAnonymously, []),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithCredential(
    _i3.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithCredential, [credential]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#signInWithCredential, [credential]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithCustomToken(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithCustomToken, [token]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#signInWithCustomToken, [token]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#signInWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithEmailLink({
    required String? email,
    required String? emailLink,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailLink, [], {
              #email: email,
              #emailLink: emailLink,
            }),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#signInWithEmailLink, [], {
                  #email: email,
                  #emailLink: emailLink,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithAuthProvider(
    _i3.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithAuthProvider, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#signInWithAuthProvider, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithProvider(
    _i3.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithProvider, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#signInWithProvider, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.ConfirmationResult> signInWithPhoneNumber(
    String? phoneNumber, [
    _i4.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [phoneNumber, verifier]),
            returnValue: _i6.Future<_i4.ConfirmationResult>.value(
              _FakeConfirmationResult_3(
                this,
                Invocation.method(#signInWithPhoneNumber, [
                  phoneNumber,
                  verifier,
                ]),
              ),
            ),
          )
          as _i6.Future<_i4.ConfirmationResult>);

  @override
  _i6.Future<_i4.UserCredential> signInWithPopup(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPopup, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#signInWithPopup, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<void> signInWithRedirect(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithRedirect, [provider]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<String> verifyPasswordResetCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPasswordResetCode, [code]),
            returnValue: _i6.Future<String>.value(
              _i7.dummyValue<String>(
                this,
                Invocation.method(#verifyPasswordResetCode, [code]),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  _i6.Future<void> verifyPhoneNumber({
    String? phoneNumber,
    _i3.PhoneMultiFactorInfo? multiFactorInfo,
    required _i3.PhoneVerificationCompleted? verificationCompleted,
    required _i3.PhoneVerificationFailed? verificationFailed,
    required _i3.PhoneCodeSent? codeSent,
    required _i3.PhoneCodeAutoRetrievalTimeout? codeAutoRetrievalTimeout,
    String? autoRetrievedSmsCodeForTesting,
    Duration? timeout = const Duration(seconds: 30),
    int? forceResendingToken,
    _i3.MultiFactorSession? multiFactorSession,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #phoneNumber: phoneNumber,
              #multiFactorInfo: multiFactorInfo,
              #verificationCompleted: verificationCompleted,
              #verificationFailed: verificationFailed,
              #codeSent: codeSent,
              #codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
              #autoRetrievedSmsCodeForTesting: autoRetrievedSmsCodeForTesting,
              #timeout: timeout,
              #forceResendingToken: forceResendingToken,
              #multiFactorSession: multiFactorSession,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> revokeTokenWithAuthorizationCode(
    String? authorizationCode,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#revokeTokenWithAuthorizationCode, [
              authorizationCode,
            ]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [UserCredential].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserCredential extends _i1.Mock implements _i4.UserCredential {
  MockUserCredential() {
    _i1.throwOnMissingStub(this);
  }
}

/// A class which mocks [User].
///
/// See the documentation for Mockito's code generation for more information.
class MockUser extends _i1.Mock implements _i4.User {
  MockUser() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get emailVerified =>
      (super.noSuchMethod(Invocation.getter(#emailVerified), returnValue: false)
          as bool);

  @override
  bool get isAnonymous =>
      (super.noSuchMethod(Invocation.getter(#isAnonymous), returnValue: false)
          as bool);

  @override
  _i3.UserMetadata get metadata =>
      (super.noSuchMethod(
            Invocation.getter(#metadata),
            returnValue: _FakeUserMetadata_4(
              this,
              Invocation.getter(#metadata),
            ),
          )
          as _i3.UserMetadata);

  @override
  List<_i3.UserInfo> get providerData =>
      (super.noSuchMethod(
            Invocation.getter(#providerData),
            returnValue: <_i3.UserInfo>[],
          )
          as List<_i3.UserInfo>);

  @override
  String get uid =>
      (super.noSuchMethod(
            Invocation.getter(#uid),
            returnValue: _i7.dummyValue<String>(this, Invocation.getter(#uid)),
          )
          as String);

  @override
  _i4.MultiFactor get multiFactor =>
      (super.noSuchMethod(
            Invocation.getter(#multiFactor),
            returnValue: _FakeMultiFactor_5(
              this,
              Invocation.getter(#multiFactor),
            ),
          )
          as _i4.MultiFactor);

  @override
  _i6.Future<void> delete() =>
      (super.noSuchMethod(
            Invocation.method(#delete, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<String?> getIdToken([bool? forceRefresh = false]) =>
      (super.noSuchMethod(
            Invocation.method(#getIdToken, [forceRefresh]),
            returnValue: _i6.Future<String?>.value(),
          )
          as _i6.Future<String?>);

  @override
  _i6.Future<_i3.IdTokenResult> getIdTokenResult([
    bool? forceRefresh = false,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#getIdTokenResult, [forceRefresh]),
            returnValue: _i6.Future<_i3.IdTokenResult>.value(
              _FakeIdTokenResult_6(
                this,
                Invocation.method(#getIdTokenResult, [forceRefresh]),
              ),
            ),
          )
          as _i6.Future<_i3.IdTokenResult>);

  @override
  _i6.Future<_i4.UserCredential> linkWithCredential(
    _i3.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithCredential, [credential]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#linkWithCredential, [credential]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> linkWithProvider(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithProvider, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#linkWithProvider, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> reauthenticateWithProvider(
    _i3.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithProvider, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#reauthenticateWithProvider, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> reauthenticateWithPopup(
    _i3.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPopup, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#reauthenticateWithPopup, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<void> reauthenticateWithRedirect(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithRedirect, [provider]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.UserCredential> linkWithPopup(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithPopup, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#linkWithPopup, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<void> linkWithRedirect(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithRedirect, [provider]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.ConfirmationResult> linkWithPhoneNumber(
    String? phoneNumber, [
    _i4.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithPhoneNumber, [phoneNumber, verifier]),
            returnValue: _i6.Future<_i4.ConfirmationResult>.value(
              _FakeConfirmationResult_3(
                this,
                Invocation.method(#linkWithPhoneNumber, [
                  phoneNumber,
                  verifier,
                ]),
              ),
            ),
          )
          as _i6.Future<_i4.ConfirmationResult>);

  @override
  _i6.Future<_i4.UserCredential> reauthenticateWithCredential(
    _i3.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithCredential, [credential]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_2(
                this,
                Invocation.method(#reauthenticateWithCredential, [credential]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<void> reload() =>
      (super.noSuchMethod(
            Invocation.method(#reload, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendEmailVerification([
    _i3.ActionCodeSettings? actionCodeSettings,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, [actionCodeSettings]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.User> unlink(String? providerId) =>
      (super.noSuchMethod(
            Invocation.method(#unlink, [providerId]),
            returnValue: _i6.Future<_i4.User>.value(
              _FakeUser_7(this, Invocation.method(#unlink, [providerId])),
            ),
          )
          as _i6.Future<_i4.User>);

  @override
  _i6.Future<void> updateEmail(String? newEmail) =>
      (super.noSuchMethod(
            Invocation.method(#updateEmail, [newEmail]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updatePassword(String? newPassword) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [newPassword]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updatePhoneNumber(
    _i3.PhoneAuthCredential? phoneCredential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updatePhoneNumber, [phoneCredential]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updateDisplayName(String? displayName) =>
      (super.noSuchMethod(
            Invocation.method(#updateDisplayName, [displayName]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updatePhotoURL(String? photoURL) =>
      (super.noSuchMethod(
            Invocation.method(#updatePhotoURL, [photoURL]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updateProfile({String? displayName, String? photoURL}) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> verifyBeforeUpdateEmail(
    String? newEmail, [
    _i3.ActionCodeSettings? actionCodeSettings,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#verifyBeforeUpdateEmail, [
              newEmail,
              actionCodeSettings,
            ]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [TtsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockTtsService extends _i1.Mock implements _i8.TtsService {
  MockTtsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set coordinateWithVoiceService(bool? value) => super.noSuchMethod(
    Invocation.setter(#coordinateWithVoiceService, value),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> speak(String? text) =>
      (super.noSuchMethod(
            Invocation.method(#speak, [text]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> stop() =>
      (super.noSuchMethod(
            Invocation.method(#stop, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> pause() =>
      (super.noSuchMethod(
            Invocation.method(#pause, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> announceReminder(String? title, {String? body}) =>
      (super.noSuchMethod(
            Invocation.method(#announceReminder, [title], {#body: body}),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> announceSearchResults(List<dynamic>? results) =>
      (super.noSuchMethod(
            Invocation.method(#announceSearchResults, [results]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [VoiceDetectionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockVoiceDetectionService extends _i1.Mock
    implements _i9.VoiceDetectionService {
  MockVoiceDetectionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void Function(String, {bool isReminderReadCommand}) get commandDetected =>
      (super.noSuchMethod(
            Invocation.getter(#commandDetected),
            returnValue: (String command, {bool? isReminderReadCommand}) {},
          )
          as void Function(String, {bool isReminderReadCommand}));

  @override
  String get recognizedText =>
      (super.noSuchMethod(
            Invocation.getter(#recognizedText),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.getter(#recognizedText),
            ),
          )
          as String);

  @override
  _i9.VoiceState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _i9.VoiceState.idle,
          )
          as _i9.VoiceState);

  @override
  bool get continuousListening =>
      (super.noSuchMethod(
            Invocation.getter(#continuousListening),
            returnValue: false,
          )
          as bool);

  @override
  set commandDetected(
    void Function(String, {bool isReminderReadCommand})? _commandDetected,
  ) => super.noSuchMethod(
    Invocation.setter(#commandDetected, _commandDetected),
    returnValueForMissingStub: null,
  );

  @override
  set continuousListening(bool? value) => super.noSuchMethod(
    Invocation.setter(#continuousListening, value),
    returnValueForMissingStub: null,
  );

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i6.Future<bool> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  void toggleContinuousListening() => super.noSuchMethod(
    Invocation.method(#toggleContinuousListening, []),
    returnValueForMissingStub: null,
  );

  @override
  void startContinuousListening() => super.noSuchMethod(
    Invocation.method(#startContinuousListening, []),
    returnValueForMissingStub: null,
  );

  @override
  void stopListening() => super.noSuchMethod(
    Invocation.method(#stopListening, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i10.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i10.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [FlutterTts].
///
/// See the documentation for Mockito's code generation for more information.
class MockFlutterTts extends _i1.Mock implements _i5.FlutterTts {
  MockFlutterTts() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<int?> get getMaxSpeechInputLength =>
      (super.noSuchMethod(
            Invocation.getter(#getMaxSpeechInputLength),
            returnValue: _i6.Future<int?>.value(),
          )
          as _i6.Future<int?>);

  @override
  _i6.Future<dynamic> get getLanguages =>
      (super.noSuchMethod(
            Invocation.getter(#getLanguages),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> get getEngines =>
      (super.noSuchMethod(
            Invocation.getter(#getEngines),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> get getDefaultEngine =>
      (super.noSuchMethod(
            Invocation.getter(#getDefaultEngine),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> get getDefaultVoice =>
      (super.noSuchMethod(
            Invocation.getter(#getDefaultVoice),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> get getVoices =>
      (super.noSuchMethod(
            Invocation.getter(#getVoices),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<_i5.SpeechRateValidRange> get getSpeechRateValidRange =>
      (super.noSuchMethod(
            Invocation.getter(#getSpeechRateValidRange),
            returnValue: _i6.Future<_i5.SpeechRateValidRange>.value(
              _FakeSpeechRateValidRange_8(
                this,
                Invocation.getter(#getSpeechRateValidRange),
              ),
            ),
          )
          as _i6.Future<_i5.SpeechRateValidRange>);

  @override
  set startHandler(_i10.VoidCallback? _startHandler) => super.noSuchMethod(
    Invocation.setter(#startHandler, _startHandler),
    returnValueForMissingStub: null,
  );

  @override
  set initHandler(_i10.VoidCallback? _initHandler) => super.noSuchMethod(
    Invocation.setter(#initHandler, _initHandler),
    returnValueForMissingStub: null,
  );

  @override
  set completionHandler(_i10.VoidCallback? _completionHandler) =>
      super.noSuchMethod(
        Invocation.setter(#completionHandler, _completionHandler),
        returnValueForMissingStub: null,
      );

  @override
  set pauseHandler(_i10.VoidCallback? _pauseHandler) => super.noSuchMethod(
    Invocation.setter(#pauseHandler, _pauseHandler),
    returnValueForMissingStub: null,
  );

  @override
  set continueHandler(_i10.VoidCallback? _continueHandler) =>
      super.noSuchMethod(
        Invocation.setter(#continueHandler, _continueHandler),
        returnValueForMissingStub: null,
      );

  @override
  set cancelHandler(_i10.VoidCallback? _cancelHandler) => super.noSuchMethod(
    Invocation.setter(#cancelHandler, _cancelHandler),
    returnValueForMissingStub: null,
  );

  @override
  set progressHandler(_i5.ProgressHandler? _progressHandler) =>
      super.noSuchMethod(
        Invocation.setter(#progressHandler, _progressHandler),
        returnValueForMissingStub: null,
      );

  @override
  set errorHandler(_i5.ErrorHandler? _errorHandler) => super.noSuchMethod(
    Invocation.setter(#errorHandler, _errorHandler),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<dynamic> awaitSpeakCompletion(bool? awaitCompletion) =>
      (super.noSuchMethod(
            Invocation.method(#awaitSpeakCompletion, [awaitCompletion]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> awaitSynthCompletion(bool? awaitCompletion) =>
      (super.noSuchMethod(
            Invocation.method(#awaitSynthCompletion, [awaitCompletion]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> speak(String? text) =>
      (super.noSuchMethod(
            Invocation.method(#speak, [text]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> pause() =>
      (super.noSuchMethod(
            Invocation.method(#pause, []),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> synthesizeToFile(String? text, String? fileName) =>
      (super.noSuchMethod(
            Invocation.method(#synthesizeToFile, [text, fileName]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setLanguage(String? language) =>
      (super.noSuchMethod(
            Invocation.method(#setLanguage, [language]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setSpeechRate(double? rate) =>
      (super.noSuchMethod(
            Invocation.method(#setSpeechRate, [rate]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setVolume(double? volume) =>
      (super.noSuchMethod(
            Invocation.method(#setVolume, [volume]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setSharedInstance(bool? sharedSession) =>
      (super.noSuchMethod(
            Invocation.method(#setSharedInstance, [sharedSession]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> autoStopSharedSession(bool? autoStop) =>
      (super.noSuchMethod(
            Invocation.method(#autoStopSharedSession, [autoStop]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setIosAudioCategory(
    _i5.IosTextToSpeechAudioCategory? category,
    List<_i5.IosTextToSpeechAudioCategoryOptions>? options, [
    _i5.IosTextToSpeechAudioMode? mode =
        _i5.IosTextToSpeechAudioMode.defaultMode,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#setIosAudioCategory, [category, options, mode]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setEngine(String? engine) =>
      (super.noSuchMethod(
            Invocation.method(#setEngine, [engine]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setPitch(double? pitch) =>
      (super.noSuchMethod(
            Invocation.method(#setPitch, [pitch]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setVoice(Map<String, String>? voice) =>
      (super.noSuchMethod(
            Invocation.method(#setVoice, [voice]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> stop() =>
      (super.noSuchMethod(
            Invocation.method(#stop, []),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> isLanguageAvailable(String? language) =>
      (super.noSuchMethod(
            Invocation.method(#isLanguageAvailable, [language]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> isLanguageInstalled(String? language) =>
      (super.noSuchMethod(
            Invocation.method(#isLanguageInstalled, [language]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> areLanguagesInstalled(List<String>? languages) =>
      (super.noSuchMethod(
            Invocation.method(#areLanguagesInstalled, [languages]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setSilence(int? timems) =>
      (super.noSuchMethod(
            Invocation.method(#setSilence, [timems]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> setQueueMode(int? queueMode) =>
      (super.noSuchMethod(
            Invocation.method(#setQueueMode, [queueMode]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  void setStartHandler(_i10.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setStartHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setInitHandler(_i10.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setInitHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setCompletionHandler(_i10.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setCompletionHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setContinueHandler(_i10.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setContinueHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setPauseHandler(_i10.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setPauseHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setCancelHandler(_i10.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setCancelHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setProgressHandler(_i5.ProgressHandler? callback) => super.noSuchMethod(
    Invocation.method(#setProgressHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setErrorHandler(_i5.ErrorHandler? handler) => super.noSuchMethod(
    Invocation.method(#setErrorHandler, [handler]),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<dynamic> platformCallHandler(_i11.MethodCall? call) =>
      (super.noSuchMethod(
            Invocation.method(#platformCallHandler, [call]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);
}

/// A class which mocks [SpeechToText].
///
/// See the documentation for Mockito's code generation for more information.
class MockSpeechToText extends _i1.Mock implements _i12.SpeechToText {
  MockSpeechToText() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get hasRecognized =>
      (super.noSuchMethod(Invocation.getter(#hasRecognized), returnValue: false)
          as bool);

  @override
  String get lastRecognizedWords =>
      (super.noSuchMethod(
            Invocation.getter(#lastRecognizedWords),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.getter(#lastRecognizedWords),
            ),
          )
          as String);

  @override
  String get lastStatus =>
      (super.noSuchMethod(
            Invocation.getter(#lastStatus),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.getter(#lastStatus),
            ),
          )
          as String);

  @override
  double get lastSoundLevel =>
      (super.noSuchMethod(Invocation.getter(#lastSoundLevel), returnValue: 0.0)
          as double);

  @override
  bool get isAvailable =>
      (super.noSuchMethod(Invocation.getter(#isAvailable), returnValue: false)
          as bool);

  @override
  bool get isListening =>
      (super.noSuchMethod(Invocation.getter(#isListening), returnValue: false)
          as bool);

  @override
  bool get isNotListening =>
      (super.noSuchMethod(
            Invocation.getter(#isNotListening),
            returnValue: false,
          )
          as bool);

  @override
  bool get hasError =>
      (super.noSuchMethod(Invocation.getter(#hasError), returnValue: false)
          as bool);

  @override
  _i6.Future<bool> get hasPermission =>
      (super.noSuchMethod(
            Invocation.getter(#hasPermission),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  set errorListener(_i12.SpeechErrorListener? _errorListener) =>
      super.noSuchMethod(
        Invocation.setter(#errorListener, _errorListener),
        returnValueForMissingStub: null,
      );

  @override
  set statusListener(_i12.SpeechStatusListener? _statusListener) =>
      super.noSuchMethod(
        Invocation.setter(#statusListener, _statusListener),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<bool> initialize({
    _i12.SpeechErrorListener? onError,
    _i12.SpeechStatusListener? onStatus,
    dynamic debugLogging = false,
    Duration? finalTimeout = const Duration(milliseconds: 2000),
    List<_i12.SpeechConfigOption>? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #onError: onError,
              #onStatus: onStatus,
              #debugLogging: debugLogging,
              #finalTimeout: finalTimeout,
              #options: options,
            }),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<void> stop() =>
      (super.noSuchMethod(
            Invocation.method(#stop, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> cancel() =>
      (super.noSuchMethod(
            Invocation.method(#cancel, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<dynamic> listen({
    _i12.SpeechResultListener? onResult,
    Duration? listenFor,
    Duration? pauseFor,
    String? localeId,
    _i12.SpeechSoundLevelChange? onSoundLevelChange,
    dynamic cancelOnError = false,
    dynamic partialResults = true,
    dynamic onDevice = false,
    _i12.ListenMode? listenMode = _i12.ListenMode.confirmation,
    dynamic sampleRate = 0,
    _i12.SpeechListenOptions? listenOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#listen, [], {
              #onResult: onResult,
              #listenFor: listenFor,
              #pauseFor: pauseFor,
              #localeId: localeId,
              #onSoundLevelChange: onSoundLevelChange,
              #cancelOnError: cancelOnError,
              #partialResults: partialResults,
              #onDevice: onDevice,
              #listenMode: listenMode,
              #sampleRate: sampleRate,
              #listenOptions: listenOptions,
            }),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  void changePauseFor(Duration? pauseFor) => super.noSuchMethod(
    Invocation.method(#changePauseFor, [pauseFor]),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<List<_i12.LocaleName>> locales() =>
      (super.noSuchMethod(
            Invocation.method(#locales, []),
            returnValue: _i6.Future<List<_i12.LocaleName>>.value(
              <_i12.LocaleName>[],
            ),
          )
          as _i6.Future<List<_i12.LocaleName>>);

  @override
  _i6.Future<_i12.LocaleName?> systemLocale() =>
      (super.noSuchMethod(
            Invocation.method(#systemLocale, []),
            returnValue: _i6.Future<_i12.LocaleName?>.value(),
          )
          as _i6.Future<_i12.LocaleName?>);
}

# Modèles YOLO pour la Reconnaissance d'Objets

## Installation du modèle YOLOv8

Pour utiliser la reconnaissance d'objets, vous devez télécharger le modèle YOLOv8 nano.

### Option 1: Téléchargement automatique (Recommandé)

Le modèle sera téléchargé automatiquement lors de la première utilisation de l'application.

### Option 2: Téléchargement manuel

1. Téléchargez le modèle YOLOv8n depuis le site officiel d'Ultralytics :
   ```
   https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt
   ```

2. <PERSON>z le fichier `yolov8n.pt` dans ce dossier (`assets/models/`)

3. Assurez-vous que le fichier est bien nommé `yolov8n.pt`

## Modèles disponibles

- **yolov8n.pt** (Nano) - ~6MB - Rapide, précision correcte
- **yolov8s.pt** (Small) - ~22MB - Équilibre vitesse/précision
- **yolov8m.pt** (Medium) - ~52MB - Bonne précision
- **yolov8l.pt** (Large) - ~87MB - Très bonne précision
- **yolov8x.pt** (Extra Large) - ~136MB - Meilleure précision

## Classes d'objets détectées

Le modèle YOLOv8 peut détecter 80 classes d'objets différentes, incluant :

- Personnes
- Véhicules (voitures, vélos, motos, bus, trains, camions)
- Animaux (chiens, chats, chevaux, moutons, vaches, etc.)
- Objets du quotidien (téléphones, ordinateurs portables, souris, claviers)
- Nourriture (pommes, bananes, oranges, etc.)
- Meubles (chaises, tables, canapés, lits)
- Et bien plus...

## Performance

- **YOLOv8n** : Recommandé pour les appareils mobiles
- Vitesse : ~50-100 FPS sur des appareils récents
- Précision : ~37% mAP sur COCO dataset

## Utilisation

Une fois le modèle installé, l'application pourra :
1. Détecter des objets en temps réel via la caméra
2. Prendre des photos et analyser les objets
3. Annoncer vocalement les objets détectés
4. Afficher les boîtes de détection avec les noms en français

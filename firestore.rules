rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Règle par défaut - refuser tout accès
    match /{document=**} {
      allow read, write: if false;
    }
    
    // Règles pour la collection reminders
    match /reminders/{reminderId} {
      // Permettre la lecture seulement si l'utilisateur est authentifié
      // et que son ID correspond à l'ID utilisateur dans le document
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      
      // Permettre l'écriture seulement si l'utilisateur est authentifié
      // et que l'ID utilisateur dans les données correspond à l'ID de l'utilisateur
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      
      // Permettre la mise à jour et la suppression seulement si l'utilisateur est authentifié
      // et que son ID correspond à l'ID utilisateur dans le document
      allow update, delete: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
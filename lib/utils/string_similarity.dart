/// Classe utilitaire pour comparer la similarité entre chaînes de caractères
class StringSimilarity {
  /// Calcule la distance de Levenshtein entre deux chaînes
  static int levenshteinDistance(String s1, String s2) {
    // Algorithme de distance de Levenshtein
    s1 = s1.toLowerCase();
    s2 = s2.toLowerCase();

    List<List<int>> d = List.generate(
      s1.length + 1,
      (i) => List.generate(s2.length + 1, (j) => 0),
    );

    for (int i = 0; i <= s1.length; i++) {
      d[i][0] = i;
    }

    for (int j = 0; j <= s2.length; j++) {
      d[0][j] = j;
    }

    for (int j = 1; j <= s2.length; j++) {
      for (int i = 1; i <= s1.length; i++) {
        int cost = (s1[i - 1] == s2[j - 1]) ? 0 : 1;
        d[i][j] = [
          d[i - 1][j] + 1, // suppression
          d[i][j - 1] + 1, // insertion
          d[i - 1][j - 1] + cost, // substitution
        ].reduce((curr, next) => curr < next ? curr : next);
      }
    }

    return d[s1.length][s2.length];
  }

  /// Calcule le score de similarité entre deux chaînes (0-1)
  static double similarity(String s1, String s2) {
    if (s1.isEmpty && s2.isEmpty) return 1.0;
    if (s1.isEmpty || s2.isEmpty) return 0.0;

    int maxLength = s1.length > s2.length ? s1.length : s2.length;
    int distance = levenshteinDistance(s1, s2);

    return 1.0 - (distance / maxLength);
  }

  /// Vérifie si les chaînes sont similaires avec un seuil donné
  static bool isSimilar(String s1, String s2, {double threshold = 0.7}) {
    return similarity(s1, s2) >= threshold;
  }
}

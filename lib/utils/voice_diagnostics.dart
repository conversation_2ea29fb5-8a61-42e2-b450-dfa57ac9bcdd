import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:permission_handler/permission_handler.dart';

/// Utilitaire pour diagnostiquer et résoudre les problèmes de reconnaissance vocale
class VoiceDiagnostics {
  /// Vérifie l'état global de la reconnaissance vocale
  static Future<Map<String, dynamic>> checkVoiceRecognitionStatus() async {
    final Map<String, dynamic> results = {};
    final stt.SpeechToText speech = stt.SpeechToText();

    // Vérifier l'initialisation
    results['isInitialized'] = await speech.initialize(
      onStatus: (status) => debugPrint('Diagnostic status: $status'),
      onError: (error) => debugPrint('Diagnostic error: ${error.errorMsg}'),
    );

    // Vérifier les permissions
    results['microphonePermission'] =
        await Permission.microphone.status.isGranted;

    // Vérifier les locales disponibles
    try {
      results['locales'] = await speech.locales();
      results['frenchLocale'] =
          results['locales']
              .where((locale) => locale.localeId.startsWith('fr_'))
              .toList();
    } catch (e) {
      results['localesError'] = e.toString();
    }

    // Vérifier l'état de l'audio
    try {
      results['isListening'] = speech.isListening;
      results['hasRecognized'] = speech.hasRecognized;
    } catch (e) {
      results['statusError'] = e.toString();
    }

    return results;
  }

  /// Réinitialise les paramètres de reconnaissance vocale
  static Future<bool> resetVoiceRecognition() async {
    try {
      // Arrêter tout processus d'écoute en cours
      final speech = stt.SpeechToText();
      if (await speech.initialize()) {
        if (speech.isListening) {
          await speech.stop();
        }
      }

      // Effacer les caches temporaires
      await _clearTemporaryFiles();

      return true;
    } catch (e) {
      debugPrint('Erreur lors de la réinitialisation: $e');
      return false;
    }
  }

  /// Nettoie les fichiers temporaires qui pourraient causer des problèmes
  static Future<void> _clearTemporaryFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final dir = Directory(tempDir.path);

      if (await dir.exists()) {
        // Ne supprime que les fichiers liés à la reconnaissance vocale
        await for (var entity in dir.list()) {
          if (entity is File &&
              (entity.path.contains('stt') ||
                  entity.path.contains('speech') ||
                  entity.path.contains('recognition'))) {
            await entity.delete();
            debugPrint('Fichier supprimé: ${entity.path}');
          }
        }
      }
    } catch (e) {
      debugPrint('Erreur lors du nettoyage des fichiers temporaires: $e');
    }
  }

  /// Génère un rapport de diagnostic pour le débogage
  static Future<String> generateDiagnosticReport() async {
    final status = await checkVoiceRecognitionStatus();
    final buffer = StringBuffer();

    buffer.writeln('=== RAPPORT DE DIAGNOSTIC DE RECONNAISSANCE VOCALE ===');
    buffer.writeln('Date: ${DateTime.now()}');
    buffer.writeln(
      'Plateforme: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}',
    );
    buffer.writeln('');

    buffer.writeln('--- STATUT ---');
    buffer.writeln('Initialisé: ${status['isInitialized']}');
    buffer.writeln('Permission micro: ${status['microphonePermission']}');
    buffer.writeln('');

    buffer.writeln('--- LOCALES ---');
    if (status.containsKey('frenchLocale')) {
      buffer.writeln(
        'Locales françaises disponibles: ${status['frenchLocale'].length}',
      );
      for (var locale in status['frenchLocale']) {
        buffer.writeln(' - ${locale.localeId}: ${locale.name}');
      }
    } else {
      buffer.writeln(
        'Erreur de récupération des locales: ${status['localesError']}',
      );
    }

    return buffer.toString();
  }
}

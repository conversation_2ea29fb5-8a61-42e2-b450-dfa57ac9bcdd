import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/theme_service.dart';

class ThemeSwitcher extends StatelessWidget {
  final double? buttonSize;

  const ThemeSwitcher({super.key, this.buttonSize});

  @override
  Widget build(BuildContext context) {
    final themeService = context.watch<ThemeService>();
    final isDark = themeService.themeMode == ThemeMode.dark;

    // Calculer la taille en fonction du buttonSize
    final double switchWidth = buttonSize != null ? buttonSize! * 2 : 60;
    final double switchHeight = buttonSize != null ? buttonSize! : 30;
    final double iconSize = buttonSize != null ? buttonSize! * 0.7 : 22;
    final double indicatorSize = switchHeight;
    final double indicatorIconSize = iconSize * 0.7;

    return GestureDetector(
      onTap: () {
        themeService.setThemeMode(isDark ? ThemeMode.light : ThemeMode.dark);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: switchWidth,
        height: switchHeight,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(switchHeight / 2),
          color:
              isDark
                  ? Colors.grey.shade800
                  : themeService.primaryColor.withOpacity(0.3),
          boxShadow: [
            BoxShadow(
              color:
                  isDark
                      ? Colors.black.withOpacity(0.2)
                      : themeService.primaryColor.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Icônes - version corrigée sans animation d'opacité
            Positioned(
              left: 5,
              top: (switchHeight - iconSize) / 2,
              child: Opacity(
                opacity: isDark ? 0.5 : 1.0,
                child: Icon(
                  Icons.wb_sunny_outlined,
                  size: iconSize,
                  color: isDark ? Colors.grey : Colors.orangeAccent,
                ),
              ),
            ),
            Positioned(
              right: 5,
              top: (switchHeight - iconSize) / 2,
              child: Opacity(
                opacity: isDark ? 1.0 : 0.5,
                child: Icon(
                  Icons.nightlight_round,
                  size: iconSize,
                  color: isDark ? Colors.lightBlueAccent : Colors.grey,
                ),
              ),
            ),
            // Indicateur
            AnimatedPositioned(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              left: isDark ? switchWidth - indicatorSize : 0,
              child: Container(
                width: indicatorSize,
                height: indicatorSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isDark ? themeService.primaryColor : Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color:
                          isDark
                              ? themeService.primaryColor.withOpacity(0.5)
                              : Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Center(
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child:
                        isDark
                            ? Icon(
                              Icons.nightlight_round,
                              key: const ValueKey('dark'),
                              size: indicatorIconSize,
                              color: Colors.white,
                            )
                            : Icon(
                              Icons.wb_sunny_outlined,
                              key: const ValueKey('light'),
                              size: indicatorIconSize,
                              color: Colors.orangeAccent,
                            ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

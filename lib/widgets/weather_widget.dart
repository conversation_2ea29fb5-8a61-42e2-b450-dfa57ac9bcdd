import 'package:flutter/material.dart';
import 'package:voice_assistant/services/weather_service.dart';

class WeatherWidget extends StatelessWidget {
  final WeatherData weatherData;
  final bool compact;

  const WeatherWidget({
    super.key,
    required this.weatherData,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    final weatherService = WeatherService();
    final iconUrl = weatherService.getWeatherIconUrl(weatherData.iconCode);

    if (compact) {
      return _buildCompactView(context, iconUrl);
    } else {
      return _buildFullView(context, iconUrl);
    }
  }

  Widget _buildCompactView(BuildContext context, String iconUrl) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.network(
            iconUrl,
            width: 40,
            height: 40,
            errorBuilder: (context, error, stackTrace) {
              return const Icon(Icons.cloud, size: 40);
            },
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${weatherData.temperature.round()}°C',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(weatherData.city, style: const TextStyle(fontSize: 12)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFullView(BuildContext context, String iconUrl) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icône météo
              Image.network(
                iconUrl,
                width: 80,
                height: 80,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const SizedBox(
                    width: 80,
                    height: 80,
                    child: Center(child: CircularProgressIndicator()),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(Icons.cloud, size: 80, color: Colors.grey);
                },
              ),

              const SizedBox(width: 16),

              // Informations principales
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    weatherData.city,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${weatherData.temperature.round()}°C',
                    style: const TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    weatherData.condition,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Informations supplémentaires
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildWeatherInfoItem(
                context,
                Icons.water_drop,
                '${weatherData.humidity}%',
                'Humidité',
              ),
              _buildWeatherInfoItem(
                context,
                Icons.air,
                '${weatherData.windSpeed.round()} km/h',
                'Vent',
              ),
              _buildWeatherInfoItem(
                context,
                Icons.thermostat,
                '${weatherData.feelsLike.round()}°C',
                'Ressenti',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherInfoItem(
    BuildContext context,
    IconData icon,
    String value,
    String label,
  ) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.secondary,
          ),
        ),
      ],
    );
  }
}

class ForecastWidget extends StatelessWidget {
  final List<ForecastData> forecastData;
  final int dayOffset;
  final bool compact;

  const ForecastWidget({
    super.key,
    required this.forecastData,
    required this.dayOffset,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    final weatherService = WeatherService();

    // Filtrer les prévisions pour le jour demandé
    final today = DateTime.now();
    final targetDate = today.add(Duration(days: dayOffset));
    final targetDateNormalized = DateTime(
      targetDate.year,
      targetDate.month,
      targetDate.day,
    );

    final dayForecasts =
        forecastData.where((f) {
          final forecastDate = DateTime(f.date.year, f.date.month, f.date.day);
          return forecastDate.isAtSameMomentAs(targetDateNormalized);
        }).toList();

    if (dayForecasts.isEmpty) {
      return const Center(
        child: Text(
          "Aucune prévision disponible pour cette date",
          textAlign: TextAlign.center,
        ),
      );
    }

    if (compact) {
      return _buildCompactView(
        context,
        dayForecasts,
        targetDate,
        weatherService,
      );
    } else {
      return _buildFullView(context, dayForecasts, targetDate, weatherService);
    }
  }

  Widget _buildCompactView(
    BuildContext context,
    List<ForecastData> dayForecasts,
    DateTime targetDate,
    WeatherService weatherService,
  ) {
    // Trouver la prévision pour le milieu de la journée (12-14h)
    final midDayForecast = dayForecasts.firstWhere(
      (f) => f.date.hour >= 12 && f.date.hour <= 14,
      orElse: () => dayForecasts.first,
    );

    final iconUrl = weatherService.getWeatherIconUrl(midDayForecast.iconCode);

    String dayText = "";
    if (dayOffset == 0)
      dayText = "Auj";
    else if (dayOffset == 1)
      dayText = "Dem";
    else {
      dayText = "${targetDate.day}/${targetDate.month}";
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            dayText,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
          Image.network(
            iconUrl,
            width: 30,
            height: 30,
            errorBuilder: (context, error, stackTrace) {
              return const Icon(Icons.cloud, size: 30);
            },
          ),
          Text(
            '${midDayForecast.temperature.round()}°C',
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildFullView(
    BuildContext context,
    List<ForecastData> dayForecasts,
    DateTime targetDate,
    WeatherService weatherService,
  ) {
    // Organiser les prévisions par période de la journée
    final morning =
        dayForecasts
            .where((f) => f.date.hour >= 6 && f.date.hour < 12)
            .toList();

    final afternoon =
        dayForecasts
            .where((f) => f.date.hour >= 12 && f.date.hour < 18)
            .toList();

    final evening = dayForecasts.where((f) => f.date.hour >= 18).toList();

    String dayText = "";
    if (dayOffset == 0)
      dayText = "Aujourd'hui";
    else if (dayOffset == 1)
      dayText = "Demain";
    else {
      dayText = "${targetDate.day}/${targetDate.month}";
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Titre
          Text(
            "Prévisions pour $dayText à ${dayForecasts[0].city}",
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Affichage des prévisions par période
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              if (morning.isNotEmpty)
                _buildForecastPeriod(
                  context,
                  "Matin",
                  morning[0],
                  weatherService,
                ),
              if (afternoon.isNotEmpty)
                _buildForecastPeriod(
                  context,
                  "Après-midi",
                  afternoon[0],
                  weatherService,
                ),
              if (evening.isNotEmpty)
                _buildForecastPeriod(
                  context,
                  "Soir",
                  evening[0],
                  weatherService,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildForecastPeriod(
    BuildContext context,
    String period,
    ForecastData forecast,
    WeatherService weatherService,
  ) {
    final iconUrl = weatherService.getWeatherIconUrl(forecast.iconCode);

    return Column(
      children: [
        Text(period, style: const TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 4),
        Image.network(
          iconUrl,
          width: 50,
          height: 50,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(Icons.cloud, size: 50);
          },
        ),
        Text(
          '${forecast.temperature.round()}°C',
          style: const TextStyle(fontSize: 18),
        ),
        Text(
          forecast.condition,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

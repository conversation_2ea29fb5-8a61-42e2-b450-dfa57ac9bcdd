import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/voice_detection_service.dart';
import 'package:android_intent_plus/android_intent.dart';

class AwesomeNotificationsService {
  static final AwesomeNotificationsService _instance =
      AwesomeNotificationsService._internal();
  factory AwesomeNotificationsService() => _instance;

  final TtsService _ttsService = TtsService();
  final VoiceDetectionService _voiceDetectionService = VoiceDetectionService();
  bool _isInitialized = false;

  static const String _channelId = 'reminders_channel';
  static const String _channelName = 'Rappels';
  static const String _channelDesc =
      'Notifications pour les rappels et aide vocale';

  AwesomeNotificationsService._internal();

  Future<void> initialize() async {
    if (_isInitialized) return;

    await AwesomeNotifications().initialize(
      null, // null pour utiliser l'icône par défaut
      [
        NotificationChannel(
          channelKey: _channelId,
          channelName: _channelName,
          channelDescription: _channelDesc,
          defaultColor: const Color(0xFF2196F3),
          ledColor: const Color(0xFF2196F3),
          importance: NotificationImportance.Max,
          channelShowBadge: true,
          enableVibration: true,
          enableLights: true,
          playSound: true,
          criticalAlerts: true,
          soundSource: 'resource://raw/notification_sound',
          defaultRingtoneType: DefaultRingtoneType.Alarm,
        ),
      ],
      debug: true,
    );

    await AwesomeNotifications().setListeners(
      onActionReceivedMethod: _onActionReceivedMethod,
      onNotificationCreatedMethod: _onNotificationCreatedMethod,
      onNotificationDisplayedMethod: _onNotificationDisplayedMethod,
      onDismissActionReceivedMethod: _onDismissActionReceivedMethod,
    );

    await _requestPermissions();
    _isInitialized = true;
    print('Service de notifications initialisé avec succès');
  }

  Future<void> _requestPermissions() async {
    await AwesomeNotifications().isNotificationAllowed().then((
      isAllowed,
    ) async {
      if (!isAllowed) {
        await AwesomeNotifications().requestPermissionToSendNotifications(
          channelKey: _channelId,
          permissions: [
            NotificationPermission.Alert,
            NotificationPermission.Sound,
            NotificationPermission.Badge,
            NotificationPermission.Vibration,
            NotificationPermission.Light,
            NotificationPermission.CriticalAlert,
            NotificationPermission.FullScreenIntent,
          ],
        );
      }
    });
  }

  @pragma('vm:entry-point')
  static Future<void> _onActionReceivedMethod(
    ReceivedAction receivedAction,
  ) async {
    final instance = AwesomeNotificationsService();
    await instance._handleActionReceived(receivedAction);
  }

  Future<void> _handleActionReceived(ReceivedAction receivedAction) async {
    bool wasListening = _voiceDetectionService.continuousListening;
    if (wasListening) {
      _voiceDetectionService.stopListening();
    }

    try {
      await _openReminderScreen();

      if (receivedAction.payload != null &&
          receivedAction.payload!.isNotEmpty) {
        final String title = receivedAction.payload!['title'] ?? '';

        await Future.delayed(const Duration(milliseconds: 500));
        await _ttsService.announceReminder(
          title,
          body: "Il est temps pour votre rappel!",
        );
      }
    } catch (e) {
      print('Erreur lors du traitement de la notification: $e');
    } finally {
      if (wasListening) {
        await Future.delayed(const Duration(seconds: 3));
        _voiceDetectionService.startContinuousListening();
      }
    }
  }

  @pragma('vm:entry-point')
  static Future<void> _onNotificationCreatedMethod(
    ReceivedNotification receivedNotification,
  ) async {
    print('Notification créée: ${receivedNotification.title}');
  }

  @pragma('vm:entry-point')
  static Future<void> _onNotificationDisplayedMethod(
    ReceivedNotification receivedNotification,
  ) async {
    print('Notification affichée: ${receivedNotification.title}');
  }

  @pragma('vm:entry-point')
  static Future<void> _onDismissActionReceivedMethod(
    ReceivedAction receivedAction,
  ) async {
    print('Notification fermée: ${receivedAction.title}');
  }

  Future<void> _openReminderScreen() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      try {
        const AndroidIntent intent = AndroidIntent(
          action: 'android.intent.action.VIEW',
          package: 'com.example.voice_assistant',
          componentName: 'com.example.voice_assistant.MainActivity',
          data: 'voiceassistant://reminders',
          flags: <int>[0x10000000, 0x04000000],
        );
        await intent.launch();
      } catch (e) {
        print('Erreur lors de l\'ouverture de l\'écran des rappels: $e');
      }
    }
  }

  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      bool wasListening = _voiceDetectionService.continuousListening;
      if (wasListening) {
        _voiceDetectionService.stopListening();
      }

      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: _channelId,
          title: title,
          body: body,
          payload: {'title': title},
          notificationLayout: NotificationLayout.Default,
          category: NotificationCategory.Alarm,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
          autoDismissible: false,
          locked: true,
          displayOnBackground: true,
          displayOnForeground: true,
        ),
      );

      await _openReminderScreen();

      await Future.delayed(const Duration(milliseconds: 500));
      await _ttsService.announceReminder(title, body: body);

      if (wasListening) {
        await Future.delayed(const Duration(seconds: 3));
        _voiceDetectionService.startContinuousListening();
      }

      print('Notification immédiate affichée: $title');
    } catch (e) {
      print('Erreur lors de l\'affichage de la notification: $e');
    }
  }

  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: _channelId,
          title: title,
          body: body,
          payload: {'title': title},
          notificationLayout: NotificationLayout.Default,
          category: NotificationCategory.Alarm,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
          autoDismissible: false,
          locked: true,
          displayOnBackground: true,
          displayOnForeground: true,
        ),
        schedule: NotificationCalendar.fromDate(
          date: scheduledDate,
          preciseAlarm: true,
          allowWhileIdle: true,
          repeats: false,
        ),
      );

      print(
        'Notification planifiée pour: ${scheduledDate.toString()}: $title - $body',
      );
    } catch (e) {
      print('Erreur lors de la planification de la notification: $e');
    }
  }

  Future<void> cancelNotification(int id) async {
    await AwesomeNotifications().cancel(id);
  }

  Future<void> cancelAllNotifications() async {
    await AwesomeNotifications().cancelAll();
  }
}

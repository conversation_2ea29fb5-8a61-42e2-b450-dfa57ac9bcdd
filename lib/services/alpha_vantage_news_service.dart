import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:voice_assistant/config/api_keys.dart';
import 'package:voice_assistant/services/deepl_translation_service.dart';
import 'package:voice_assistant/services/gemini_chat_service.dart';

/// Service d'actualités utilisant Alpha Vantage API
class AlphaVantageNewsService {
  static AlphaVantageNewsService? _instance;
  
  static AlphaVantageNewsService get instance {
    _instance ??= AlphaVantageNewsService._internal();
    return _instance!;
  }
  
  AlphaVantageNewsService._internal();
  
  factory AlphaVantageNewsService() => instance;
  
  final GeminiChatService _geminiService = GeminiChatService();
  final DeepLTranslationService _translationService = DeepLTranslationService.instance;
  
  static const String _baseUrl = 'https://www.alphavantage.co/query';
  
  /// Vérifie si le service est disponible
  bool get isAvailable => ApiKeys.alphaVantageApiKey != 'VOTRE_CLE_ALPHA_VANTAGE_ICI';
  
  /// Récupère les actualités financières et générales
  Future<List<AlphaVantageNewsArticle>?> getNews({
    String topics = 'technology,finance,general',
    int limit = 10,
  }) async {
    if (!isAvailable) {
      debugPrint('Service Alpha Vantage non configuré');
      return null;
    }
    
    try {
      debugPrint('Récupération des actualités Alpha Vantage...');
      
      final response = await http.get(
        Uri.parse(_baseUrl).replace(queryParameters: {
          'function': 'NEWS_SENTIMENT',
          'topics': topics,
          'limit': limit.toString(),
          'apikey': ApiKeys.alphaVantageApiKey,
        }),
      );
      
      debugPrint('Alpha Vantage Response Status: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['feed'] != null) {
          final List<dynamic> articlesJson = data['feed'];
          
          final articles = <AlphaVantageNewsArticle>[];
          for (final json in articlesJson.take(limit)) {
            final article = AlphaVantageNewsArticle.fromJson(json);
            if (article.title.isNotEmpty && article.summary.isNotEmpty) {
              // Traduire l'article en français
              final translatedArticle = await _translateArticle(article);
              articles.add(translatedArticle);
            }
          }
          
          debugPrint('${articles.length} articles récupérés et traduits');
          return articles;
        }
      } else {
        debugPrint('❌ Erreur Alpha Vantage: ${response.statusCode} - ${response.body}');
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ Erreur lors de la récupération des actualités: $e');
      return null;
    }
  }
  
  /// Traduit un article en français
  Future<AlphaVantageNewsArticle> _translateArticle(AlphaVantageNewsArticle article) async {
    try {
      // Détecter si l'article est déjà en français
      final detectedLanguage = await _translationService.detectLanguage(article.title);
      
      if (detectedLanguage == 'fr') {
        return article; // Déjà en français
      }
      
      // Traduire le titre
      final titleResult = await _translationService.translateText(
        text: article.title,
        targetLanguage: 'français',
      );
      
      // Traduire le résumé
      final summaryResult = await _translationService.translateText(
        text: article.summary,
        targetLanguage: 'français',
      );
      
      // Créer un nouvel article avec les traductions
      return AlphaVantageNewsArticle(
        title: titleResult?.translatedText ?? article.title,
        summary: summaryResult?.translatedText ?? article.summary,
        url: article.url,
        timePublished: article.timePublished,
        authors: article.authors,
        source: article.source,
        sentiment: article.sentiment,
        topics: article.topics,
      );
    } catch (e) {
      debugPrint('Erreur lors de la traduction de l\'article: $e');
      return article; // Retourner l'article original en cas d'erreur
    }
  }
  
  /// Génère un résumé des actualités avec Gemini
  Future<String?> generateNewsSummary(List<AlphaVantageNewsArticle> articles) async {
    if (articles.isEmpty) return null;
    
    try {
      final articlesText = articles.take(5).map((article) => 
        '• ${article.title}\n  ${article.summary.length > 200 ? article.summary.substring(0, 200) + "..." : article.summary}'
      ).join('\n\n');
      
      final prompt = '''
Voici les dernières actualités. Fais-moi un résumé concis et informatif en français :

$articlesText

Résume les points clés en 3-4 phrases maximum, en français.
''';
      
      return await _geminiService.sendMessage(prompt);
    } catch (e) {
      debugPrint('Erreur lors de la génération du résumé: $e');
      return null;
    }
  }
}

/// Article d'actualité Alpha Vantage
class AlphaVantageNewsArticle {
  final String title;
  final String summary;
  final String url;
  final String timePublished;
  final List<String> authors;
  final String source;
  final Map<String, dynamic>? sentiment;
  final List<Map<String, dynamic>>? topics;
  
  AlphaVantageNewsArticle({
    required this.title,
    required this.summary,
    required this.url,
    required this.timePublished,
    required this.authors,
    required this.source,
    this.sentiment,
    this.topics,
  });
  
  /// Crée un article depuis un JSON Alpha Vantage
  factory AlphaVantageNewsArticle.fromJson(Map<String, dynamic> json) {
    return AlphaVantageNewsArticle(
      title: json['title'] ?? '',
      summary: json['summary'] ?? '',
      url: json['url'] ?? '',
      timePublished: json['time_published'] ?? '',
      authors: List<String>.from(json['authors'] ?? []),
      source: json['source'] ?? '',
      sentiment: json['overall_sentiment_score'] != null 
        ? {'score': json['overall_sentiment_score'], 'label': json['overall_sentiment_label']}
        : null,
      topics: json['topics'] != null 
        ? List<Map<String, dynamic>>.from(json['topics'])
        : null,
    );
  }
  
  /// Obtient une description formatée
  String getFormattedDescription() {
    final buffer = StringBuffer();
    buffer.write('📰 $title\n\n');
    buffer.write('📝 $summary\n\n');
    buffer.write('🔗 Source: $source\n');
    buffer.write('📅 ${_formatDate(timePublished)}');
    
    if (sentiment != null) {
      final score = sentiment!['score'];
      final label = sentiment!['label'];
      buffer.write('\n📊 Sentiment: $label ($score)');
    }
    
    return buffer.toString();
  }
  
  String _formatDate(String dateString) {
    try {
      if (dateString.length >= 8) {
        final year = dateString.substring(0, 4);
        final month = dateString.substring(4, 6);
        final day = dateString.substring(6, 8);
        return '$day/$month/$year';
      }
    } catch (e) {
      // Ignore
    }
    return dateString;
  }
  
  @override
  String toString() {
    return 'AlphaVantageNewsArticle(title: "$title", source: "$source")';
  }
}

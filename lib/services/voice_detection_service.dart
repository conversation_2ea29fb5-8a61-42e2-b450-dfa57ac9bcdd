import 'dart:async';
// Import pour utiliser la reconnaissance vocale réelle
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';
import 'package:voice_assistant/utils/string_similarity.dart';

enum VoiceState {
  idle, // En attente
  listening, // Écoute active
  processing, // Traitement de la commande
  error, // Erreur
}

/// Service de détection vocale
/// Gère la reconnaissance vocale avec mode réel et simulé
class VoiceDetectionService with ChangeNotifier {
  static VoiceDetectionService? _instance;
  static void resetInstance() {
    _instance = null;
  }

  factory VoiceDetectionService({stt.SpeechToText? speechToText}) {
    _instance ??= VoiceDetectionService._internal(speechToText: speechToText);
    return _instance!;
  }

  final stt.SpeechToText _speech;
  bool _initialized = false;
  bool _simulated = false; // Mode simulé désactivé par défaut
  bool _continuousListening = false; // Écoute en continu désactivée par défaut

  String _recognizedText = '';
  String get recognizedText => _recognizedText;

  // Nouveau: stockage du dernier texte traité pour éviter les doublons
  String _lastProcessedText = '';

  // Nouvelle propriété pour détecter si l'utilisateur parle
  bool _isSpeaking = false;
  bool get isSpeaking => _isSpeaking;

  // Nouvelle propriété pour le niveau sonore actuel
  double _soundLevel = 0.0;
  double get soundLevel => _soundLevel;

  VoiceState _state = VoiceState.idle;
  VoiceState get state => _state;

  Timer? _restartListeningTimer;
  Timer? _silenceTimer;
  bool _isIntentionallyPaused = false;

  VoiceDetectionService._internal({stt.SpeechToText? speechToText})
    : _speech = speechToText ?? stt.SpeechToText();

  /// Initialise le service de reconnaissance vocale
  /// Retourne true si l'initialisation réussit
  Future<bool> initialize() async {
    if (_initialized) return true;

    try {
      if (await Permission.microphone.request().isGranted) {
        _initialized = await _speech.initialize(
          onStatus: _onStatusChange,
          onError: _onErrorListener,
          debugLogging: true, // Activer les logs de débogage
        );

        if (!_initialized) {
          _activateSimulatedMode('Échec d\'initialisation du service');
        }

        return _initialized || _simulated;
      } else {
        _activateSimulatedMode('Permission du microphone refusée');
        return _simulated;
      }
    } catch (e) {
      _activateSimulatedMode('Erreur d\'initialisation: $e');
      return _simulated;
    }
  }

  /// Active le mode simulé avec un message d'erreur
  void _activateSimulatedMode(String reason) {
    _simulated = true;
    debugPrint('$reason. Mode simulé activé.');
  }

  // Getter et setter pour l'écoute continue
  bool get continuousListening => _continuousListening;
  set continuousListening(bool value) {
    _continuousListening = value;
    _isIntentionallyPaused = !value;
    notifyListeners();

    if (_continuousListening) {
      startContinuousListening();
    } else {
      stopListening();
    }
  }

  /// Bascule l'état d'écoute continue (active/désactive)
  void toggleContinuousListening() {
    continuousListening = !continuousListening;
  }

  void _onStatusChange(String status) {
    debugPrint('État reconnaissance vocale: $status');

    if (status == 'done' || status == 'notListening') {
      _setState(VoiceState.idle);
      _isSpeaking = false;

      // Ne redémarre l'écoute que si l'écoute continue est activée et pas en pause intentionnelle
      if (_continuousListening && !_isIntentionallyPaused) {
        _restartContinuousListening();
      }
    } else if (status == 'listening') {
      _setState(VoiceState.listening);
    }
  }

  void _onErrorListener(dynamic error) {
    debugPrint('Erreur de reconnaissance vocale: ${error.errorMsg}');
    _setError("Erreur: ${error.errorMsg}");
    _isSpeaking = false;

    // Ne redémarre l'écoute que si l'écoute continue est activée et pas en pause intentionnelle
    if (_continuousListening && !_isIntentionallyPaused) {
      _restartContinuousListening();
    }
  }

  void _setError(String errorMessage) {
    _recognizedText = errorMessage;
    _setState(VoiceState.error);
    notifyListeners();
  }

  void _setState(VoiceState newState) {
    _state = newState;
    notifyListeners();
  }

  void startContinuousListening() async {
    _isIntentionallyPaused = false;
    _lastProcessedText = '';

    if (!_initialized) {
      bool success = await initialize();
      if (!success) return;
    }

    if (_simulated) {
      // En mode simulé, on simule une écoute active
      _setState(VoiceState.listening);
      print('Écoute simulée active...');

      // Simuler une détection de commande après quelques secondes
      Future.delayed(Duration(seconds: 5), () {
        if (_state == VoiceState.listening) {
          _simulateCommand();
        }
      });
    } else {
      // Utiliser la vraie API en mode réel
      _startListening();
    }
  }

  void _simulateCommand() {
    _recognizedText = "comment vas-tu";
    _isSpeaking = true;
    notifyListeners();

    // Traiter directement la commande après un délai
    Future.delayed(Duration(seconds: 1), () {
      _recognizeCommand(_recognizedText);
      _isSpeaking = false;
    });
  }

  void _startListening() async {
    if (_speech.isListening) return;

    try {
      await _speech.listen(
        onResult: _onResultListener,
        listenFor: const Duration(seconds: 60), // Augmenté à 60 secondes
        pauseFor: const Duration(seconds: 3), // Attendre 3 secondes de silence
        partialResults: true,
        localeId: 'fr_FR',
        cancelOnError: false,
        listenMode:
            stt
                .ListenMode
                .confirmation, // Mode confirmation pour plus de stabilité
        onSoundLevelChange:
            _onSoundLevelChange, // Nouvelle méthode pour suivre le niveau sonore
      );
    } catch (e) {
      _setError("Erreur de démarrage d'écoute: $e");
    }
  }

  // Nouvelle méthode pour surveiller le niveau sonore
  void _onSoundLevelChange(double level) {
    _soundLevel = level;

    // Considère que l'utilisateur parle si le niveau sonore dépasse un certain seuil
    bool wasSpeeking = _isSpeaking;
    _isSpeaking = level > 0.015; // Seuil ajustable selon vos tests

    // Réinitialise le timer de silence à chaque fois qu'il y a du son
    if (_isSpeaking) {
      _silenceTimer?.cancel();
    }
    // Si l'utilisateur vient d'arrêter de parler, commencer à surveiller le silence
    else if (wasSpeeking && !_isSpeaking) {
      _startSilenceDetection();
    }

    notifyListeners();
  }

  // Nouvelle méthode pour commencer la détection de silence
  void _startSilenceDetection() {
    _silenceTimer?.cancel();
    _silenceTimer = Timer(const Duration(milliseconds: 1500), () {
      // Si le texte reconnu a une longueur significative et qu'il n'a pas déjà été traité
      if (_recognizedText.length > 2 && _recognizedText != _lastProcessedText) {
        _recognizeCommand(_recognizedText);
        _lastProcessedText = _recognizedText;
      }
    });
  }

  void stopListening() async {
    _isIntentionallyPaused = true;
    _silenceTimer?.cancel();

    if (_simulated) {
      _setState(VoiceState.idle);
    } else {
      await _speech.stop();
    }
  }

  void _restartContinuousListening() {
    // Ne fait rien si l'écoute continue est désactivée ou pause intentionnelle
    if (!_continuousListening || _isIntentionallyPaused) return;

    _restartListeningTimer?.cancel();
    _restartListeningTimer = Timer(const Duration(seconds: 1), () {
      // Augmenté à 1 seconde
      if (_state != VoiceState.processing) {
        if (_simulated) {
          // En mode simulé
          _setState(VoiceState.listening);
          Future.delayed(Duration(seconds: 5), () {
            if (_state == VoiceState.listening) {
              _simulateCommand();
            }
          });
        } else {
          // En mode réel
          _startListening();
        }
      }
    });
  }

  void _onResultListener(dynamic result) {
    // Mettre à jour le texte reconnu
    _recognizedText = result.recognizedWords;
    notifyListeners();

    // Si le résultat est final, traiter la commande
    if (result.finalResult) {
      // Traiter la commande uniquement si elle n'a pas déjà été traitée
      if (_recognizedText.isNotEmpty && _recognizedText != _lastProcessedText) {
        _recognizeCommand(_recognizedText);
        _lastProcessedText = _recognizedText;
      }
    }
  }

  void _recognizeCommand(String text) {
    // Plus besoin de filtrer le mot-clé, on traite tout le texte
    final String command = text.toLowerCase().trim();

    if (command.isEmpty) return;

    _setState(VoiceState.processing);
    _isIntentionallyPaused = true; // Pause intentionnelle pendant le traitement

    // Vérifier s'il s'agit d'une commande pour lire les rappels
    bool isReminderReadCommand = _isReminderReadCommand(command);

    // Emettre la commande détectée pour qu'elle soit traitée par l'application
    commandDetected(command, isReminderReadCommand: isReminderReadCommand);

    // Désactiver l'écoute continue après exécution de la commande
    _continuousListening = false;

    // Rétablir l'état d'inactivité après un délai
    Future.delayed(Duration(seconds: 2), () {
      _setState(VoiceState.idle);
      // Maintenir _isIntentionallyPaused à true pour éviter la reprise automatique
    });
  }

  // Méthode pour détecter si la commande concerne la lecture d'un rappel
  bool _isReminderReadCommand(String command) {
    final List<String> reminderReadPatterns = [
      'lis',
      'lire',
      'dit',
      'dire',
      'annonce',
      'annoncer',
      'énoncer',
      'énonce',
      'liste',
      'affiche',
      'montre',
      'rappels',
      'quels sont mes rappels',
      'montre-moi mes rappels',
    ];

    for (final pattern in reminderReadPatterns) {
      if (command.contains(pattern) && command.contains('rappel')) {
        return true;
      }
    }

    return false;
  }

  // Cette fonction sera appelée par les écouteurs externes
  void Function(String command, {bool isReminderReadCommand}) commandDetected =
      (_, {isReminderReadCommand = false}) {};

  @override
  void dispose() {
    stopListening();
    _restartListeningTimer?.cancel();
    _silenceTimer?.cancel();
    super.dispose();
  }
}

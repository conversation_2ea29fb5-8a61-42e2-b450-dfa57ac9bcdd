import 'package:audio_service/audio_service.dart';
import 'package:flutter/material.dart';
import 'package:audio_session/audio_session.dart';

class MyAudioHandler extends BaseAudioHandler {
  @override
  Future<void> play() async {
    playbackState.add(
      playbackState.value.copyWith(
        playing: true,
        processingState: AudioProcessingState.ready,
      ),
    );
  }

  @override
  Future<void> pause() async {
    playbackState.add(playbackState.value.copyWith(playing: false));
  }

  @override
  Future<void> stop() async {
    playbackState.add(
      playbackState.value.copyWith(
        playing: false,
        processingState: AudioProcessingState.idle,
      ),
    );
  }

  void updatePlaybackState({bool isPlaying = true}) {
    playbackState.add(
      playbackState.value.copyWith(
        playing: isPlaying,
        processingState:
            isPlaying ? AudioProcessingState.ready : AudioProcessingState.idle,
      ),
    );
  }
}

class AudioConfig {
  static Future<void> initialize() async {
    // Configurer la session audio globale
    final session = await AudioSession.instance;
    await session.configure(
      AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.duckOthers,
        avAudioSessionMode: AVAudioSessionMode.spokenAudio,
        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.audibilityEnforced,
          usage: AndroidAudioUsage.assistanceAccessibility,
        ),
        androidAudioFocusGainType:
            AndroidAudioFocusGainType.gainTransientMayDuck,
        androidWillPauseWhenDucked: false,
        // Paramètres optimisés pour la reconnaissance vocale
      ),
    );

    // Activer la session audio
    await session.setActive(true);

    // Initialiser le service audio
    await AudioService.init(
      builder: () => MyAudioHandler(),
      config: const AudioServiceConfig(
        androidNotificationChannelId:
            'com.example.voice_assistant.channel.audio',
        androidNotificationChannelName: 'Assistance vocale',
        androidNotificationOngoing: false,
        androidStopForegroundOnPause: true,
        androidNotificationChannelDescription: 'Playback vocale pour rappels',
        notificationColor: Color(0xFF6C63FF),
      ),
    );

    debugPrint('Configuration audio initialisée avec succès');
  }

  /// Force la réinitialisation de la session audio
  /// Utile en cas de problèmes de reconnaissance vocale
  static Future<void> resetAudioSession() async {
    try {
      final session = await AudioSession.instance;

      // Désactiver puis réactiver la session
      await session.setActive(false);
      await Future.delayed(const Duration(milliseconds: 500));

      // Reconfigurer et réactiver
      await session.configure(
        AudioSessionConfiguration(
          avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
          avAudioSessionCategoryOptions:
              AVAudioSessionCategoryOptions.duckOthers,
          avAudioSessionMode: AVAudioSessionMode.spokenAudio,
          androidAudioAttributes: const AndroidAudioAttributes(
            contentType: AndroidAudioContentType.speech,
            flags: AndroidAudioFlags.audibilityEnforced,
            usage: AndroidAudioUsage.assistanceAccessibility,
          ),
          androidAudioFocusGainType:
              AndroidAudioFocusGainType.gainTransientMayDuck,
          androidWillPauseWhenDucked: false,
        ),
      );

      await session.setActive(true);
      debugPrint('Session audio réinitialisée avec succès');
    } catch (e) {
      debugPrint('Erreur lors de la réinitialisation de la session audio: $e');
    }
  }
}

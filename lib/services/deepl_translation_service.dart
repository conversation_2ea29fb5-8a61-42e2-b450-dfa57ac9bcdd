import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:voice_assistant/config/api_keys.dart';

/// Service de traduction utilisant DeepL API
class DeepLTranslationService {
  static DeepLTranslationService? _instance;
  
  static DeepLTranslationService get instance {
    _instance ??= DeepLTranslationService._internal();
    return _instance!;
  }
  
  DeepLTranslationService._internal();
  
  factory DeepLTranslationService() => instance;
  
  static const String _baseUrl = 'https://api-free.deepl.com/v2';
  
  /// Vérifie si le service est disponible
  bool get isAvailable => ApiKeys.deepLApiKey != 'VOTRE_CLE_DEEPL_ICI';
  
  /// Mapping des codes de langue pour DeepL
  static const Map<String, String> _languageCodes = {
    'français': 'FR',
    'anglais': 'EN',
    'espagnol': 'ES',
    'allemand': 'DE',
    'italien': 'IT',
    'portugais': 'PT',
    'russe': 'RU',
    'japonais': 'JA',
    'chinois': 'ZH',
    'néerlandais': 'NL',
    'polonais': 'PL',
    'suédois': 'SV',
    'danois': 'DA',
    'finnois': 'FI',
    'grec': 'EL',
    'hongrois': 'HU',
    'tchèque': 'CS',
    'slovaque': 'SK',
    'slovène': 'SL',
    'estonien': 'ET',
    'letton': 'LV',
    'lituanien': 'LT',
    'bulgare': 'BG',
    'roumain': 'RO',
    'arabe': 'AR',
    'coréen': 'KO',
    'norvégien': 'NB',
    'turc': 'TR',
    'ukrainien': 'UK',
    'indonésien': 'ID',
  };
  
  /// Traduit un texte vers la langue cible
  Future<DeepLTranslationResult?> translateText({
    required String text,
    required String targetLanguage,
    String? sourceLanguage,
  }) async {
    if (!isAvailable) {
      debugPrint('Service DeepL non configuré');
      return null;
    }
    
    if (text.trim().isEmpty) {
      debugPrint('Texte vide pour la traduction');
      return null;
    }
    
    try {
      final String targetCode = _getLanguageCode(targetLanguage);
      if (targetCode.isEmpty) {
        debugPrint('Langue cible non supportée: $targetLanguage');
        return null;
      }
      
      debugPrint('Traduction DeepL: "$text" vers $targetLanguage ($targetCode)');
      
      final Map<String, String> body = {
        'auth_key': ApiKeys.deepLApiKey,
        'text': text,
        'target_lang': targetCode,
      };
      
      if (sourceLanguage != null) {
        final String sourceCode = _getLanguageCode(sourceLanguage);
        if (sourceCode.isNotEmpty) {
          body['source_lang'] = sourceCode;
        }
      }
      
      final response = await http.post(
        Uri.parse('$_baseUrl/translate'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: body,
      );
      
      debugPrint('DeepL Response Status: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['translations'] != null && data['translations'].isNotEmpty) {
          final translation = data['translations'][0];
          final translatedText = translation['text'];
          final detectedSourceLang = translation['detected_source_language'];
          
          debugPrint('✅ Traduction DeepL réussie: "$translatedText"');
          
          return DeepLTranslationResult(
            originalText: text,
            translatedText: translatedText,
            sourceLanguage: detectedSourceLang?.toLowerCase() ?? 'auto',
            targetLanguage: targetCode.toLowerCase(),
          );
        }
      } else {
        debugPrint('❌ Erreur DeepL: ${response.statusCode} - ${response.body}');
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ Erreur lors de la traduction DeepL: $e');
      return null;
    }
  }
  
  /// Obtient le code de langue DeepL
  String _getLanguageCode(String language) {
    final normalizedLanguage = language.toLowerCase().trim();
    return _languageCodes[normalizedLanguage] ?? '';
  }
  
  /// Vérifie si une langue est supportée
  bool isLanguageSupported(String language) {
    return _getLanguageCode(language).isNotEmpty;
  }
  
  /// Obtient la liste des langues supportées
  List<String> getSupportedLanguages() {
    return _languageCodes.keys.toList()..sort();
  }
  
  /// Détecte la langue d'un texte (heuristique simple)
  Future<String?> detectLanguage(String text) async {
    if (text.trim().isEmpty) return null;
    
    // Heuristique simple basée sur des mots courants
    final String lowerText = text.toLowerCase();
    
    // Mots français
    final List<String> frenchWords = ['le', 'la', 'les', 'de', 'du', 'des', 'et', 'ou', 'comment', 'bonjour', 'merci'];
    // Mots anglais
    final List<String> englishWords = ['the', 'and', 'or', 'how', 'hello', 'thank', 'you', 'with', 'from'];
    
    int frenchScore = 0;
    int englishScore = 0;
    
    for (String word in frenchWords) {
      if (lowerText.contains(word)) frenchScore++;
    }
    
    for (String word in englishWords) {
      if (lowerText.contains(word)) englishScore++;
    }
    
    if (frenchScore > englishScore) {
      return 'fr';
    } else if (englishScore > frenchScore) {
      return 'en';
    } else {
      return 'fr'; // Par défaut français
    }
  }
}

/// Résultat de traduction DeepL
class DeepLTranslationResult {
  final String originalText;
  final String translatedText;
  final String sourceLanguage;
  final String targetLanguage;
  
  DeepLTranslationResult({
    required this.originalText,
    required this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
  });
  
  @override
  String toString() {
    return 'DeepLTranslationResult(original: "$originalText", translated: "$translatedText", $sourceLanguage -> $targetLanguage)';
  }
}

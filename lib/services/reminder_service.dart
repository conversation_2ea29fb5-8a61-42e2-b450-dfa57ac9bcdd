import '../services/firebase/reminders_service.dart';

class ReminderService {
  final RemindersService _firebaseRemindersService = RemindersService();

  // Ajouter un nouveau rappel
  Future<String> addReminder(String title, DateTime dateTime) {
    return _firebaseRemindersService.addReminder(title, dateTime);
  }

  // Obtenir tous les rappels de l'utilisateur
  Stream<List<Reminder>> getUserReminders() {
    return _firebaseRemindersService.getUserReminders();
  }

  // Obtenir les rappels filtrés par état de complétion
  Stream<List<Reminder>> getFilteredReminders({bool? isCompleted}) {
    return _firebaseRemindersService.getFilteredReminders(
      isCompleted: isCompleted,
    );
  }

  // Mettre à jour un rappel
  Future<void> updateReminder(Reminder reminder) {
    return _firebaseRemindersService.updateReminder(reminder);
  }

  // Marquer un rappel comme terminé
  Future<void> markReminderAsCompleted(String reminderId) {
    return _firebaseRemindersService.markReminderAsCompleted(reminderId);
  }

  // Basculer l'état d'un rappel (terminé/non terminé)
  Future<void> toggleReminderCompletion(String reminderId, bool currentState) {
    return _firebaseRemindersService.toggleReminderCompletion(
      reminderId,
      currentState,
    );
  }

  // Supprimer un rappel
  Future<void> deleteReminder(String reminderId) {
    return _firebaseRemindersService.deleteReminder(reminderId);
  }

  // Obtenir tous les rappels de l'utilisateur en une seule fois
  Future<List<Reminder>> getUserRemindersOnce() {
    return _firebaseRemindersService.getUserRemindersOnce();
  }

  // Obtenir les rappels à venir
  Stream<List<Reminder>> getUpcomingReminders() {
    return _firebaseRemindersService.getUpcomingReminders();
  }
}

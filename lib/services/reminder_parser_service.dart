import 'package:intl/intl.dart';

/// Service pour analyser les commandes de rappel vocales
class ReminderParserService {
  static final ReminderParserService _instance =
      ReminderParserService._internal();
  factory ReminderParserService() => _instance;

  ReminderParserService._internal();

  /// Analyse une commande vocale pour extraire les informations de rappel
  /// Retourne un Map contenant la durée et le message du rappel
  /// Si la commande n'est pas un rappel, retourne null
  Map<String, dynamic>? parseReminderCommand(String command) {
    final String normalizedCommand = command.toLowerCase().trim();

    // Vérifier si c'est une commande de rappel
    if (!isReminderCommand(normalizedCommand)) {
      return null;
    }

    // Extraire la durée et le message
    final durationInfo = _extractDuration(normalizedCommand);

    if (durationInfo == null) {
      return null;
    }

    final message = _extractMessage(normalizedCommand, durationInfo);

    return {
      'duration': durationInfo['duration'],
      'targetDate': durationInfo['targetDate'],
      'message': message,
    };
  }

  /// Vérifie si la commande est une demande de rappel
  bool isReminderCommand(String command) {
    final List<String> reminderKeywords = [
      'rappelle-moi',
      'rappelle moi',
      'rappeler',
      'rappel',
      'alarme',
      'alerte',
      'notification',
      'préviens-moi',
      'préviens moi',
      'avertis-moi',
      'avertis moi',
    ];

    for (final keyword in reminderKeywords) {
      if (command.contains(keyword)) {
        return true;
      }
    }

    return false;
  }

  /// Extrait la durée du rappel à partir de la commande
  /// Retourne un Map contenant la durée en secondes et la date cible
  Map<String, dynamic>? _extractDuration(String command) {
    // Pattern pour "dans X minutes/heures/secondes"
    final RegExp relativeTimeRegex = RegExp(
      r'dans\s+(\d+)\s+(minute|minutes|heure|heures|seconde|secondes|jour|jours)',
      caseSensitive: false,
    );

    // Pattern pour "à X heures" ou "à Xh"
    final RegExp specificTimeRegex = RegExp(
      r'à\s+(\d+)(?:\s*[h:]?\s*(\d+)?)?(?:\s*(du matin|de l|du soir))?',
      caseSensitive: false,
    );

    // Pattern pour "demain", "après-demain"
    final RegExp specificDayRegex = RegExp(
      r'(demain|ce soir)',
      caseSensitive: false,
    );

    // Vérifier si c'est une durée relative (dans X minutes/heures)
    final relativeMatch = relativeTimeRegex.firstMatch(command);
    if (relativeMatch != null) {
      final int quantity = int.parse(relativeMatch.group(1)!);
      final String unit = relativeMatch.group(2)!.toLowerCase();

      int durationInSeconds;

      if (unit.contains('seconde')) {
        durationInSeconds = quantity;
      } else if (unit.contains('minute')) {
        durationInSeconds = quantity * 60;
      } else if (unit.contains('heure')) {
        durationInSeconds = quantity * 60 * 60;
      } else if (unit.contains('jour')) {
        durationInSeconds = quantity * 24 * 60 * 60;
      } else {
        return null;
      }

      final DateTime now = DateTime.now();
      final DateTime targetDate = now.add(Duration(seconds: durationInSeconds));

      return {'duration': durationInSeconds, 'targetDate': targetDate};
    }

    // Vérifier si c'est une heure spécifique (à X heures)
    final specificTimeMatch = specificTimeRegex.firstMatch(command);
    if (specificTimeMatch != null) {
      final int hours = int.parse(specificTimeMatch.group(1)!);
      final String? minutesStr = specificTimeMatch.group(2);
      final int minutes = minutesStr != null ? int.parse(minutesStr) : 0;

      final String? periodStr = specificTimeMatch.group(3);

      int adjustedHours = hours;

      // Ajuster l'heure en fonction de la période (matin, après-midi, soir)
      if (periodStr != null) {
        if (periodStr.contains('soir')) {
          if (hours < 12) {
            adjustedHours += 12;
          }
        }
      } else if (hours < 12 && DateTime.now().hour >= 12) {
        // Si l'heure est exprimée sans période et qu'elle est < 12,
        // et qu'il est déjà l'après-midi, supposer qu'il s'agit de l'après-midi
        adjustedHours += 12;
      }

      final DateTime now = DateTime.now();
      DateTime targetDate = DateTime(
        now.year,
        now.month,
        now.day,
        adjustedHours,
        minutes,
      );

      // Si l'heure cible est déjà passée aujourd'hui, planifier pour demain
      if (targetDate.isBefore(now)) {
        targetDate = targetDate.add(const Duration(days: 1));
      }

      final int durationInSeconds = targetDate.difference(now).inSeconds;

      return {'duration': durationInSeconds, 'targetDate': targetDate};
    }

    // Vérifier si c'est un jour spécifique (demain, après-demain)
    final specificDayMatch = specificDayRegex.firstMatch(command);
    if (specificDayMatch != null) {
      final String daySpecifier = specificDayMatch.group(1)!.toLowerCase();

      final DateTime now = DateTime.now();
      DateTime targetDate;

      if (daySpecifier == 'demain') {
        // Créer une date pour demain à 9h00
        targetDate = DateTime(now.year, now.month, now.day + 1, 9, 0);
      } else if (daySpecifier == 'ce soir') {
        // Créer une date pour ce soir à 20h00
        targetDate = DateTime(now.year, now.month, now.day, 20, 0);
      } else {
        return null;
      }

      final int durationInSeconds = targetDate.difference(now).inSeconds;

      return {'duration': durationInSeconds, 'targetDate': targetDate};
    }

    return null;
  }

  /// Extrait le message du rappel à partir de la commande
  String _extractMessage(String command, Map<String, dynamic> durationInfo) {
    // Patterns pour chercher le message après certains marqueurs
    final List<String> messageMarkers = ['de ', 'pour ', 'que ', 'concernant '];

    String message = '';

    // Tenter d'extraire le message après les marqueurs
    for (final marker in messageMarkers) {
      if (command.contains(marker)) {
        final parts = command.split(marker);
        if (parts.length > 1) {
          message = parts.sublist(1).join(marker).trim();
          break;
        }
      }
    }

    // Si aucun message trouvé avec les marqueurs, utiliser une approche plus générique
    if (message.isEmpty) {
      // Suppression des mots-clés de rappel et de durée
      String cleanedCommand = command;

      // Supprimer les mots-clés de rappel
      final List<String> reminderKeywords = [
        'rappelle-moi',
        'rappelle moi',
        'rappeler',
        'rappel',
        'alarme',
        'alerte',
        'notification',
        'préviens-moi',
        'préviens moi',
        'avertis-moi',
        'avertis moi',
      ];

      for (final keyword in reminderKeywords) {
        cleanedCommand = cleanedCommand.replaceAll(keyword, '');
      }

      // Supprimer les expressions de durée
      final List<RegExp> durationPatterns = [
        RegExp(
          r'dans\s+\d+\s+(minute|minutes|heure|heures|seconde|secondes|jour|jours)',
          caseSensitive: false,
        ),
        RegExp(r'à\s+\d+(?:\s*[h:]?\s*\d+)?', caseSensitive: false),
        RegExp(r'(demain|ce soir)', caseSensitive: false),
      ];

      for (final pattern in durationPatterns) {
        cleanedCommand = cleanedCommand.replaceAll(pattern, '');
      }

      message = cleanedCommand.trim();

      // Supprimer les prépositions isolées au début
      final List<String> prepositions = [
        'de',
        'pour',
        'que',
        'le',
        'la',
        'les',
        'du',
        'des',
      ];
      for (final preposition in prepositions) {
        if (message.startsWith('$preposition ')) {
          message = message.substring(preposition.length + 1).trim();
        }
      }
    }

    // Si le message est vide, utiliser un message par défaut
    if (message.isEmpty) {
      final DateTime targetDate = durationInfo['targetDate'] as DateTime;
      final String formattedTime = DateFormat('HH:mm').format(targetDate);
      message = "Rappel pour $formattedTime";
    }

    return message;
  }

  /// Format une date cible en texte convivial pour la confirmation vocale
  String formatReminderConfirmation(DateTime targetDate, String message) {
    final DateTime now = DateTime.now();
    final DateFormat timeFormat = DateFormat('HH:mm');
    final String formattedTime = timeFormat.format(targetDate);

    String timeDescription;

    if (targetDate.day == now.day) {
      timeDescription = "aujourd'hui à $formattedTime";
    } else if (targetDate.day == now.day + 1) {
      timeDescription = "demain à $formattedTime";
    } else {
      final DateFormat dateFormat = DateFormat('EEEE d MMMM', 'fr_FR');
      timeDescription = "le ${dateFormat.format(targetDate)} à $formattedTime";
    }

    return "J'ai créé un rappel pour $timeDescription : $message";
  }
}

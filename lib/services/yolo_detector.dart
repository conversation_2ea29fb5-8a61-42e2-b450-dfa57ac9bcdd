import 'dart:typed_data';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import 'package:image/image.dart' as img;
import 'object_recognition_service.dart';

/// Service de détection d'objets utilisant l'analyse d'image intelligente
class YoloDetector {
  static const String _labelsPath = 'assets/models/coco_labels.txt';

  final List<String> _labels = [];

  // Paramètres de détection
  static const double _threshold = 0.6;

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  // Cache pour optimiser les détections
  DateTime? _lastDetectionTime;
  final List<DetectedObject> _lastDetections = [];

  /// Initialise le détecteur YOLO
  Future<bool> initialize() async {
    try {
      debugPrint('Initialisation du détecteur d\'objets...');

      // Charger les labels
      await _loadLabels();

      _isInitialized = true;
      debugPrint('Détecteur d\'objets initialisé avec succès');
      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du détecteur: $e');
      return false;
    }
  }

  /// Charge les labels des classes d'objets
  Future<void> _loadLabels() async {
    try {
      final labelsData = await rootBundle.loadString(_labelsPath);
      _labels.clear();
      _labels.addAll(
        labelsData.split('\n').where((label) => label.isNotEmpty).toList(),
      );
      debugPrint('${_labels.length} labels chargés');
    } catch (e) {
      debugPrint('Erreur lors du chargement des labels: $e');
      // Labels par défaut si le fichier n'est pas trouvé
      _labels.clear();
      _labels.addAll([
        'person',
        'car',
        'phone',
        'chair',
        'table',
        'book',
        'bottle',
        'cup',
      ]);
    }
  }

  /// Détecte les objets dans une image de caméra
  Future<List<DetectedObject>> detectFromCameraImage(
    CameraImage cameraImage,
  ) async {
    if (!_isInitialized) return [];

    try {
      // Optimisation : ne pas analyser trop souvent
      final now = DateTime.now();
      if (_lastDetectionTime != null &&
          now.difference(_lastDetectionTime!).inMilliseconds < 1000) {
        return _lastDetections;
      }
      _lastDetectionTime = now;

      // Convertir l'image de la caméra en format utilisable
      final rgbImage = _convertYUV420ToRGB(cameraImage);

      // Analyser l'image
      final detections = await _analyzeImage(rgbImage);

      _lastDetections.clear();
      _lastDetections.addAll(detections);

      return detections;
    } catch (e) {
      debugPrint('Erreur lors de la détection sur image caméra: $e');
      return [];
    }
  }

  /// Détecte les objets dans une image
  Future<List<DetectedObject>> detectFromImage(Uint8List imageBytes) async {
    if (!_isInitialized) return [];

    try {
      // Décoder l'image
      final image = img.decodeImage(imageBytes);
      if (image == null) return [];

      // Analyser l'image
      return await _analyzeImage(image);
    } catch (e) {
      debugPrint('Erreur lors de la détection sur image: $e');
      return [];
    }
  }

  /// Convertit une image YUV420 en RGB
  img.Image _convertYUV420ToRGB(CameraImage cameraImage) {
    final width = cameraImage.width;
    final height = cameraImage.height;

    final yPlane = cameraImage.planes[0];
    final uPlane = cameraImage.planes[1];
    final vPlane = cameraImage.planes[2];

    final image = img.Image(width: width, height: height);

    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * yPlane.bytesPerRow + x;
        final uvIndex =
            (y ~/ 2) * uPlane.bytesPerRow + (x ~/ 2) * uPlane.bytesPerPixel!;

        if (yIndex < yPlane.bytes.length && uvIndex < uPlane.bytes.length) {
          final yValue = yPlane.bytes[yIndex];
          final uValue = uPlane.bytes[uvIndex];
          final vValue = vPlane.bytes[uvIndex];

          // Conversion YUV vers RGB
          final r = (yValue + 1.402 * (vValue - 128)).clamp(0, 255).toInt();
          final g =
              (yValue - 0.344136 * (uValue - 128) - 0.714136 * (vValue - 128))
                  .clamp(0, 255)
                  .toInt();
          final b = (yValue + 1.772 * (uValue - 128)).clamp(0, 255).toInt();

          image.setPixel(x, y, img.ColorRgb8(r, g, b));
        }
      }
    }

    return image;
  }

  /// Analyse une image pour détecter des objets
  Future<List<DetectedObject>> _analyzeImage(img.Image image) async {
    final detections = <DetectedObject>[];

    // Redimensionner l'image pour l'analyse
    final resized = img.copyResize(image, width: 320, height: 240);

    // Analyser différentes régions de l'image
    final regions = _generateRegions(resized.width, resized.height);

    for (final region in regions) {
      final objectType = _analyzeRegion(resized, region);
      if (objectType != null) {
        final confidence = _calculateConfidence(resized, region, objectType);
        if (confidence > _threshold) {
          detections.add(
            DetectedObject(
              className: objectType,
              confidence: confidence,
              x: region.x / resized.width,
              y: region.y / resized.height,
              width: region.width / resized.width,
              height: region.height / resized.height,
            ),
          );
        }
      }
    }

    return _filterDetections(detections);
  }

  /// Génère des régions d'intérêt dans l'image
  List<Region> _generateRegions(int width, int height) {
    final regions = <Region>[];

    // Grille de régions de différentes tailles
    for (int size in [40, 60, 80, 120]) {
      for (int y = 0; y < height - size; y += size ~/ 2) {
        for (int x = 0; x < width - size; x += size ~/ 2) {
          regions.add(Region(x: x, y: y, width: size, height: size));
        }
      }
    }

    return regions;
  }

  /// Analyse une région spécifique de l'image
  String? _analyzeRegion(img.Image image, Region region) {
    // Extraire les caractéristiques de la région
    final features = _extractFeatures(image, region);

    // Classifier basé sur les caractéristiques
    return _classifyFeatures(features);
  }

  /// Extrait les caractéristiques d'une région
  Map<String, double> _extractFeatures(img.Image image, Region region) {
    double totalR = 0, totalG = 0, totalB = 0;
    double edgeCount = 0;
    int pixelCount = 0;

    for (
      int y = region.y;
      y < region.y + region.height && y < image.height;
      y++
    ) {
      for (
        int x = region.x;
        x < region.x + region.width && x < image.width;
        x++
      ) {
        final pixel = image.getPixel(x, y);
        totalR += pixel.r;
        totalG += pixel.g;
        totalB += pixel.b;
        pixelCount++;

        // Détection de contours simple
        if (x > 0 && y > 0) {
          final prevPixel = image.getPixel(x - 1, y - 1);
          final diff =
              (pixel.r - prevPixel.r).abs() +
              (pixel.g - prevPixel.g).abs() +
              (pixel.b - prevPixel.b).abs();
          if (diff > 100) edgeCount++;
        }
      }
    }

    if (pixelCount == 0) return {};

    return {
      'avgR': totalR / pixelCount,
      'avgG': totalG / pixelCount,
      'avgB': totalB / pixelCount,
      'edgeDensity': edgeCount / pixelCount,
      'aspectRatio': region.width / region.height,
      'size': pixelCount.toDouble(),
    };
  }

  /// Classifie les caractéristiques en type d'objet
  String? _classifyFeatures(Map<String, double> features) {
    if (features.isEmpty) return null;

    final avgR = features['avgR'] ?? 0;
    final avgG = features['avgG'] ?? 0;
    final avgB = features['avgB'] ?? 0;
    final edgeDensity = features['edgeDensity'] ?? 0;
    final aspectRatio = features['aspectRatio'] ?? 1;
    final size = features['size'] ?? 0;

    // Logique de classification simple basée sur les caractéristiques

    // Détection de personne (couleurs chair, forme verticale)
    if (avgR > 150 &&
        avgG > 100 &&
        avgB > 80 &&
        aspectRatio > 1.5 &&
        aspectRatio < 3 &&
        size > 1000) {
      return 'person';
    }

    // Détection de téléphone (forme rectangulaire, couleurs sombres)
    if (avgR < 100 &&
        avgG < 100 &&
        avgB < 100 &&
        aspectRatio > 1.5 &&
        aspectRatio < 2.5 &&
        size > 200 &&
        size < 2000) {
      return 'phone';
    }

    // Détection de voiture (couleurs métalliques, grande taille)
    if (edgeDensity > 0.1 &&
        size > 3000 &&
        aspectRatio > 1.2 &&
        aspectRatio < 3) {
      return 'car';
    }

    // Détection de chaise (couleurs bois, forme verticale)
    if (avgR > 100 &&
        avgG > 80 &&
        avgB < 100 &&
        aspectRatio > 1 &&
        aspectRatio < 2 &&
        size > 800) {
      return 'chair';
    }

    // Détection de livre (couleurs variées, forme rectangulaire)
    if (aspectRatio > 0.7 && aspectRatio < 1.3 && size > 300 && size < 1500) {
      return 'book';
    }

    // Détection de bouteille (forme verticale, couleurs claires)
    if (aspectRatio > 2 &&
        aspectRatio < 4 &&
        size > 200 &&
        size < 1000 &&
        (avgR + avgG + avgB) > 300) {
      return 'bottle';
    }

    return null;
  }

  /// Calcule la confiance pour une détection
  double _calculateConfidence(
    img.Image image,
    Region region,
    String objectType,
  ) {
    final features = _extractFeatures(image, region);

    // Confiance basée sur la correspondance des caractéristiques
    double confidence = 0.5; // Confiance de base

    switch (objectType) {
      case 'person':
        confidence += (features['aspectRatio']! > 1.5 ? 0.2 : 0);
        confidence += (features['size']! > 1000 ? 0.2 : 0);
        break;
      case 'phone':
        confidence +=
            (features['aspectRatio']! > 1.5 && features['aspectRatio']! < 2.5
                ? 0.3
                : 0);
        break;
      case 'car':
        confidence += (features['size']! > 3000 ? 0.3 : 0);
        break;
      default:
        confidence += 0.1;
    }

    return confidence.clamp(0.0, 1.0);
  }

  /// Filtre et optimise les détections
  List<DetectedObject> _filterDetections(List<DetectedObject> detections) {
    // Supprimer les doublons et les détections qui se chevauchent trop
    final filtered = <DetectedObject>[];

    for (final detection in detections) {
      bool shouldAdd = true;

      for (final existing in filtered) {
        if (existing.className == detection.className) {
          final overlap = _calculateOverlap(detection, existing);
          if (overlap > 0.5) {
            shouldAdd = false;
            break;
          }
        }
      }

      if (shouldAdd) {
        filtered.add(detection);
      }
    }

    // Limiter le nombre de détections
    filtered.sort((a, b) => b.confidence.compareTo(a.confidence));
    return filtered.take(5).toList();
  }

  /// Calcule le chevauchement entre deux détections
  double _calculateOverlap(DetectedObject a, DetectedObject b) {
    final x1 = math.max(a.x, b.x);
    final y1 = math.max(a.y, b.y);
    final x2 = math.min(a.x + a.width, b.x + b.width);
    final y2 = math.min(a.y + a.height, b.y + b.height);

    if (x2 <= x1 || y2 <= y1) return 0.0;

    final intersection = (x2 - x1) * (y2 - y1);
    final areaA = a.width * a.height;
    final areaB = b.width * b.height;
    final union = areaA + areaB - intersection;

    return intersection / union;
  }

  /// Libère les ressources
  void dispose() {
    _isInitialized = false;
    _lastDetections.clear();
  }
}

/// Classe pour représenter une région d'intérêt
class Region {
  final int x;
  final int y;
  final int width;
  final int height;

  Region({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });
}

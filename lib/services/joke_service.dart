import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'dart:math';

class JokeService {
  static final JokeService _instance = JokeService._internal();
  factory JokeService() => _instance;

  JokeService._internal();

  // Récupérer une blague depuis l'API JokeAPI ou locale
  Future<Joke?> getRandomJoke() async {
    try {
      // API principale - JokeAPI avec support français
      final url = Uri.parse(
        'https://v2.jokeapi.dev/joke/Any?lang=fr&blacklistFlags=religious,racist',
      );

      final response = await http.get(url).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // Si pas de blagues en français disponibles ou erreur, utiliser nos blagues prédéfinies
        if (data['error'] == true) {
          debugPrint(
            'Erreur de l\'API JokeAPI, utilisation des blagues locales',
          );
          return _getLocalJoke();
        }

        return Joke.fromJson(data);
      } else {
        debugPrint('Statut HTTP non-200: ${response.statusCode}');
        return _getLocalJoke();
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération de la blague: $e');

      // En cas d'erreur de connexion, tenter une API alternative
      try {
        final alternativeUrl = Uri.parse('https://blague.xyz/api/joke/random');

        final alternativeResponse = await http
            .get(alternativeUrl)
            .timeout(const Duration(seconds: 3));

        if (alternativeResponse.statusCode == 200) {
          final data = json.decode(alternativeResponse.body);

          // Format différent pour cette API
          return Joke(
            category: "Blague française",
            type: "single",
            joke:
                data['joke']['text'] ??
                "Pourquoi les développeurs n'aiment pas la nature ? Parce qu'elle a trop de bugs.",
            language: "fr",
          );
        }
      } catch (secondError) {
        debugPrint('Échec de l\'API alternative: $secondError');
      }

      // Si tout échoue, utiliser nos blagues prédéfinies
      return _getLocalJoke();
    }
  }

  // Collection de blagues en français
  Joke _getLocalJoke() {
    final jokes = [
      "Pourquoi les développeurs n'aiment pas la nature ? Parce qu'elle a trop de bugs.",
      "Un informaticien ne descend pas du métro. Il libère la RAM.",
      "Comment appelle-t-on un chat tombé dans un pot de peinture le jour de Noël ? Un chat-peint de Noël !",
      "Que dit un développeur quand il se noie ? « F5 ! F5 ! »",
      "Pourquoi les programmeurs préfèrent-ils le froid ? Parce que ça bug moins.",
      "Quelle est la différence entre un développeur et un escalier ? Aucune, ils te cassent les deux pieds quand ils ne fonctionnent pas.",
      "Comment un développeur tente-t-il de réparer sa voiture ? Il sort de la voiture, ferme toutes les fenêtres, retourne dans la voiture, et redémarre.",
      "Comment reconnaît-on un développeur extraverti ? C'est celui qui regarde les chaussures des autres quand il parle.",
      "Combien faut-il de développeurs pour changer une ampoule ? Aucun, c'est un problème matériel.",
      "Qu'est-ce qu'un développeur dit à un autre quand le code est compliqué ? Café.parse().",
      "Deux octets se rencontrent. Le premier demande: 'Ça va?' L'autre répond: 'Oui, et bit?'",
      "Un bug entre dans un bar. Le barman dit: 'Nous avons une nouvelle fonctionnalité!'",
    ];

    final randomIndex = Random().nextInt(jokes.length);

    return Joke(
      category: "Blague française",
      type: "single",
      joke: jokes[randomIndex],
      language: "fr",
    );
  }
}

class Joke {
  final String category;
  final String type;
  final String joke;
  final String language;

  Joke({
    required this.category,
    required this.type,
    required this.joke,
    required this.language,
  });

  factory Joke.fromJson(Map<String, dynamic> json) {
    // Vérifier si c'est une blague de type "twopart" (question/réponse)
    if (json['type'] == 'twopart') {
      final setup = json['setup'] ?? '';
      final delivery = json['delivery'] ?? '';
      // Combiner la configuration et la livraison pour former une blague complète
      return Joke(
        category: json['category'] ?? 'Unknown',
        type: json['type'] ?? 'twopart',
        joke: '$setup\n$delivery',
        language: json['lang'] ?? 'fr',
      );
    }

    // Sinon, c'est une blague de type "single"
    return Joke(
      category: json['category'] ?? 'Unknown',
      type: json['type'] ?? 'single',
      joke: json['joke'] ?? 'Désolé, je n\'ai pas de blague pour le moment.',
      language: json['lang'] ?? 'fr',
    );
  }

  @override
  String toString() {
    return joke;
  }
}

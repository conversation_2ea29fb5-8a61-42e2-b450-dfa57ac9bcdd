import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:voice_assistant/config/api_keys.dart';

class WebSearchService {
  static final WebSearchService _instance = WebSearchService._internal();
  factory WebSearchService() => _instance;

  WebSearchService._internal();

  // Utiliser la clé API depuis le fichier de configuration
  final String _apiKey = ApiKeys.googleSearchApiKey;
  final String _searchEngineId = ApiKeys.googleSearchEngineId;

  // Effectuer une recherche web
  Future<List<SearchResult>> search(String query) async {
    try {
      // URL de l'API Google Custom Search
      final url = Uri.parse(
        'https://www.googleapis.com/customsearch/v1'
        '?key=$_apiKey'
        '&cx=$_searchEngineId'
        '&q=${Uri.encodeComponent(query)}'
        '&num=5' // Limiter à 5 résultats
        '&lr=lang_fr', // Résultats en français
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<SearchResult> results = [];

        if (data['items'] != null) {
          for (var item in data['items']) {
            results.add(
              SearchResult(
                title: item['title'] ?? 'Sans titre',
                description: item['snippet'] ?? '',
                url: item['link'] ?? '',
              ),
            );
          }
        }

        return results;
      } else {
        throw Exception('Erreur lors de la requête: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Erreur lors de la recherche web: $e');
      return [
        SearchResult(
          title: 'Erreur de recherche',
          description: 'Impossible de terminer la recherche: $e',
          url: '',
        ),
      ];
    }
  }

  // Méthode pour obtenir des suggestions de recherche
  Future<List<String>> getSuggestions(String query) async {
    try {
      // URL pour les suggestions Google
      final url = Uri.parse(
        'https://suggestqueries.google.com/complete/search'
        '?client=firefox'
        '&q=${Uri.encodeComponent(query)}'
        '&hl=fr',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        if (data.length > 1 && data[1] is List) {
          return List<String>.from(data[1]);
        }
        return [];
      } else {
        return [];
      }
    } catch (e) {
      debugPrint('Erreur lors de la recherche de suggestions: $e');
      return [];
    }
  }

  // Méthode pour obtenir une réponse rapide
  Future<String> getQuickAnswer(String query) async {
    try {
      // Utilise la même API que pour la recherche
      final searchResults = await search(query);

      if (searchResults.isNotEmpty) {
        return searchResults.first.description;
      } else {
        return 'Aucune réponse rapide trouvée';
      }
    } catch (e) {
      debugPrint('Erreur lors de la recherche de réponse rapide: $e');
      return 'Erreur lors de la recherche de réponse rapide';
    }
  }
}

class SearchResult {
  final String title;
  final String description;
  final String url;

  SearchResult({
    required this.title,
    required this.description,
    required this.url,
  });

  @override
  String toString() {
    return '$title: $description ($url)';
  }
}

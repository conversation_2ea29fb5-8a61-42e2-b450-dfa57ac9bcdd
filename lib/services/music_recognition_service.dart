import 'package:flutter/foundation.dart';

/// Service de reconnaissance musicale simplifié
/// Version temporaire sans dépendances audio complexes
class MusicRecognitionService {
  static MusicRecognitionService? _instance;
  
  static MusicRecognitionService get instance {
    _instance ??= MusicRecognitionService._internal();
    return _instance!;
  }
  
  MusicRecognitionService._internal();
  
  factory MusicRecognitionService() => instance;
  
  bool _isRecording = false;
  bool _isRecognizing = false;
  
  /// Vérifie si le service est disponible (temporairement désactivé)
  bool get isAvailable => false; // Temporairement désactivé
  
  /// Vérifie si l'enregistrement est en cours
  bool get isRecording => _isRecording;
  
  /// Vérifie si la reconnaissance est en cours
  bool get isRecognizing => _isRecognizing;
  
  /// Démarre la reconnaissance musicale (version simplifiée)
  Future<MusicRecognitionResult?> recognizeMusic({
    int durationSeconds = 12,
  }) async {
    debugPrint('Service de reconnaissance musicale temporairement désactivé');
    return MusicRecognitionResult.error(
      'La reconnaissance musicale est temporairement désactivée. '
      'Cette fonctionnalité sera disponible dans une prochaine mise à jour.'
    );
  }
  
  /// Arrête l'enregistrement en cours (placeholder)
  Future<void> stopRecording() async {
    _isRecording = false;
    debugPrint('Arrêt de l\'enregistrement (placeholder)');
  }
  
  /// Libère les ressources (placeholder)
  Future<void> dispose() async {
    await stopRecording();
    debugPrint('Libération des ressources (placeholder)');
  }
}

/// Résultat de la reconnaissance musicale
class MusicRecognitionResult {
  final bool isSuccess;
  final bool isNotFound;
  final String? title;
  final String? artist;
  final String? album;
  final String? releaseDate;
  final int? durationMs;
  final int? confidence;
  final String? errorMessage;
  
  MusicRecognitionResult._({
    required this.isSuccess,
    required this.isNotFound,
    this.title,
    this.artist,
    this.album,
    this.releaseDate,
    this.durationMs,
    this.confidence,
    this.errorMessage,
  });
  
  /// Crée un résultat de succès
  factory MusicRecognitionResult.success({
    required String title,
    required String artist,
    required String album,
    required String releaseDate,
    required int durationMs,
    required int confidence,
  }) {
    return MusicRecognitionResult._(
      isSuccess: true,
      isNotFound: false,
      title: title,
      artist: artist,
      album: album,
      releaseDate: releaseDate,
      durationMs: durationMs,
      confidence: confidence,
    );
  }
  
  /// Crée un résultat "non trouvé"
  factory MusicRecognitionResult.notFound(String message) {
    return MusicRecognitionResult._(
      isSuccess: false,
      isNotFound: true,
      errorMessage: message,
    );
  }
  
  /// Crée un résultat d'erreur
  factory MusicRecognitionResult.error(String message) {
    return MusicRecognitionResult._(
      isSuccess: false,
      isNotFound: false,
      errorMessage: message,
    );
  }
  
  /// Obtient une description formatée du résultat
  String getFormattedResult() {
    if (isSuccess && title != null && artist != null) {
      return '"$title" par $artist';
    } else if (isNotFound) {
      return errorMessage ?? 'Musique non trouvée';
    } else {
      return errorMessage ?? 'Erreur inconnue';
    }
  }
  
  /// Obtient une description détaillée
  String getDetailedDescription() {
    if (isSuccess) {
      final buffer = StringBuffer();
      buffer.write('Titre: ${title ?? "Inconnu"}\n');
      buffer.write('Artiste: ${artist ?? "Inconnu"}\n');
      if (album != null && album!.isNotEmpty && album != 'Album inconnu') {
        buffer.write('Album: $album\n');
      }
      if (releaseDate != null && releaseDate!.isNotEmpty) {
        buffer.write('Date de sortie: $releaseDate\n');
      }
      if (confidence != null) {
        buffer.write('Confiance: $confidence%');
      }
      return buffer.toString();
    } else {
      return errorMessage ?? 'Erreur inconnue';
    }
  }
  
  @override
  String toString() {
    return 'MusicRecognitionResult(success: $isSuccess, notFound: $isNotFound, title: $title, artist: $artist, error: $errorMessage)';
  }
}

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppThemeNotifier with ChangeNotifier {
  late bool _isDarkMode;
  late SharedPreferences _prefs;
  static const String _darkModeKey = 'darkMode';

  bool get isDarkMode => _isDarkMode;

  AppThemeNotifier(bool initialDarkMode) {
    _isDarkMode = initialDarkMode;
    _initPrefs();
  }

  Future<void> _initPrefs() async {
    _prefs = await SharedPreferences.getInstance();
  }

  Future<void> toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    await _prefs.setBool(_darkModeKey, _isDarkMode);
    notifyListeners();
  }

  Future<void> setDarkMode(bool value) async {
    _isDarkMode = value;
    await _prefs.setBool(_darkModeKey, _isDarkMode);
    notifyListeners();
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';

class Reminder {
  final String id;
  final String title;
  final DateTime dateTime;
  final String userId;
  final bool isCompleted;

  Reminder({
    required this.id,
    required this.title,
    required this.dateTime,
    required this.userId,
    this.isCompleted = false,
  });

  factory Reminder.fromMap(Map<String, dynamic> map) {
    return Reminder(
      id: map['id'],
      title: map['title'],
      dateTime: (map['dateTime'] as Timestamp).toDate(),
      userId: map['userId'],
      isCompleted: map['isCompleted'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'dateTime': Timestamp.fromDate(dateTime),
      'userId': userId,
      'isCompleted': isCompleted,
    };
  }

  // Créer une copie d'un rappel avec des valeurs modifiées
  Reminder copyWith({
    String? id,
    String? title,
    DateTime? dateTime,
    String? userId,
    bool? isCompleted,
  }) {
    return Reminder(
      id: id ?? this.id,
      title: title ?? this.title,
      dateTime: dateTime ?? this.dateTime,
      userId: userId ?? this.userId,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}

class RemindersService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final Uuid _uuid = const Uuid();

  // Collection de référence pour les rappels
  CollectionReference get _remindersCollection =>
      _firestore.collection('reminders');

  // Obtenir l'utilisateur actuel
  User? get currentUser => _auth.currentUser;

  // Ajouter un nouveau rappel
  Future<String> addReminder(String title, DateTime dateTime) async {
    try {
      if (currentUser == null) {
        throw Exception("L'utilisateur n'est pas connecté");
      }

      final String id = _uuid.v4();
      final reminder = Reminder(
        id: id,
        title: title,
        dateTime: dateTime,
        userId: currentUser!.uid,
      );

      await _remindersCollection.doc(id).set(reminder.toMap());
      return id;
    } catch (e) {
      throw Exception("Erreur lors de l'ajout du rappel: $e");
    }
  }

  // Obtenir tous les rappels de l'utilisateur
  Stream<List<Reminder>> getUserReminders() {
    if (currentUser == null) {
      return Stream.value([]);
    }

    return _remindersCollection
        .where('userId', isEqualTo: currentUser!.uid)
        .orderBy('dateTime')
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map(
                (doc) => Reminder.fromMap(doc.data() as Map<String, dynamic>),
              )
              .toList();
        });
  }

  // Obtenir les rappels filtrés par état de complétion
  Stream<List<Reminder>> getFilteredReminders({bool? isCompleted}) {
    if (currentUser == null) {
      return Stream.value([]);
    }

    Query query = _remindersCollection.where(
      'userId',
      isEqualTo: currentUser!.uid,
    );

    if (isCompleted != null) {
      query = query.where('isCompleted', isEqualTo: isCompleted);
    }

    return query.orderBy('dateTime').snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => Reminder.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    });
  }

  // Mettre à jour un rappel
  Future<void> updateReminder(Reminder reminder) async {
    try {
      await _remindersCollection.doc(reminder.id).update(reminder.toMap());
    } catch (e) {
      throw Exception("Erreur lors de la mise à jour du rappel: $e");
    }
  }

  // Marquer un rappel comme terminé
  Future<void> markReminderAsCompleted(String reminderId) async {
    try {
      await _remindersCollection.doc(reminderId).update({'isCompleted': true});
    } catch (e) {
      throw Exception("Erreur lors de la mise à jour du rappel: $e");
    }
  }

  // Basculer l'état d'un rappel (terminé/non terminé)
  Future<void> toggleReminderCompletion(
    String reminderId,
    bool currentState,
  ) async {
    try {
      await _remindersCollection.doc(reminderId).update({
        'isCompleted': !currentState,
      });
    } catch (e) {
      throw Exception("Erreur lors du changement d'état du rappel: $e");
    }
  }

  // Supprimer un rappel
  Future<void> deleteReminder(String reminderId) async {
    try {
      await _remindersCollection.doc(reminderId).delete();
    } catch (e) {
      throw Exception("Erreur lors de la suppression du rappel: $e");
    }
  }

  // Obtenir tous les rappels de l'utilisateur en une seule fois (non en stream)
  Future<List<Reminder>> getUserRemindersOnce() async {
    if (currentUser == null) {
      return [];
    }

    try {
      final QuerySnapshot snapshot =
          await _firestore
              .collection('reminders')
              .where('userId', isEqualTo: currentUser!.uid)
              .orderBy('dateTime')
              .get();

      return snapshot.docs
          .map((doc) => Reminder.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print("Erreur lors de la récupération des rappels: $e");
      return [];
    }
  }

  // Obtenir les rappels à venir (non terminés et avec une date future)
  Stream<List<Reminder>> getUpcomingReminders() {
    if (currentUser == null) {
      return Stream.value([]);
    }

    final now = DateTime.now();

    return _remindersCollection
        .where('userId', isEqualTo: currentUser!.uid)
        .where('isCompleted', isEqualTo: false)
        .orderBy('dateTime')
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map(
                (doc) => Reminder.fromMap(doc.data() as Map<String, dynamic>),
              )
              .where((reminder) => reminder.dateTime.isAfter(now))
              .toList();
        });
  }
}

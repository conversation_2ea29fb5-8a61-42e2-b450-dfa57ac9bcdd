import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import 'reminders_service.dart';

class BackupService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final Uuid _uuid = const Uuid();

  // Obtenir l'utilisateur actuel
  User? get currentUser => _auth.currentUser;

  // Collection de référence pour les rappels
  CollectionReference get _remindersCollection =>
      _firestore.collection('reminders');

  // Exporter tous les rappels de l'utilisateur dans un format JSON
  Future<String> exportReminders() async {
    if (currentUser == null) {
      throw Exception("L'utilisateur n'est pas connecté");
    }

    try {
      final QuerySnapshot snapshot =
          await _remindersCollection
              .where('userId', isEqualTo: currentUser!.uid)
              .get();

      final List<Map<String, dynamic>> reminders =
          snapshot.docs
              .map(
                (doc) =>
                    Reminder.fromMap(
                      doc.data() as Map<String, dynamic>,
                    ).toMap(),
              )
              .toList();

      final Map<String, dynamic> exportData = {
        'userId': currentUser!.uid,
        'exportDate': DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
        'reminders': reminders,
      };

      return jsonEncode(exportData);
    } catch (e) {
      throw Exception("Erreur lors de l'exportation des rappels: $e");
    }
  }

  // Importer des rappels à partir d'un JSON
  Future<int> importReminders(String jsonData) async {
    if (currentUser == null) {
      throw Exception("L'utilisateur n'est pas connecté");
    }

    try {
      final Map<String, dynamic> importData = jsonDecode(jsonData);
      final List<dynamic> remindersData = importData['reminders'];

      int importedCount = 0;

      // Utiliser un batch pour importer plusieurs rappels en une seule opération
      final WriteBatch batch = _firestore.batch();

      for (var reminderData in remindersData) {
        // Créer une nouvelle ID pour le rappel importé
        final String newId = _uuid.v4();

        // Mettre à jour les données pour refléter l'utilisateur actuel et la nouvelle ID
        final Map<String, dynamic> updatedData = {
          ...reminderData,
          'id': newId,
          'userId': currentUser!.uid,
        };

        // Ajouter le rappel au batch
        batch.set(_remindersCollection.doc(newId), updatedData);
        importedCount++;
      }

      // Exécuter le batch
      await batch.commit();

      return importedCount;
    } catch (e) {
      throw Exception("Erreur lors de l'importation des rappels: $e");
    }
  }
}

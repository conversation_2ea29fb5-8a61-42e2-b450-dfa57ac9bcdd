import 'package:flutter/foundation.dart';
import 'package:voice_assistant/services/deepl_translation_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/music_recognition_service.dart';
import 'package:voice_assistant/services/alpha_vantage_news_service.dart';
import 'package:voice_assistant/utils/string_similarity.dart';

/// Service pour traiter les commandes vocales spécialisées
class VoiceCommandService {
  static VoiceCommandService? _instance;

  static VoiceCommandService get instance {
    _instance ??= VoiceCommandService._internal();
    return _instance!;
  }

  VoiceCommandService._internal();

  factory VoiceCommandService() => instance;

  final DeepLTranslationService _translationService =
      DeepLTranslationService.instance;
  final TtsService _ttsService = TtsService();
  final MusicRecognitionService _musicRecognitionService =
      MusicRecognitionService();
  final AlphaVantageNewsService _newsService = AlphaVantageNewsService.instance;

  /// Traite une commande vocale et détermine son type
  Future<bool> processCommand(String command) async {
    final String normalizedCommand = command.toLowerCase().trim();

    debugPrint('🎤 Commande reçue: "$command"');
    debugPrint('🔄 Commande normalisée: "$normalizedCommand"');

    // Vérifier si c'est une commande de traduction
    if (await _handleTranslationCommand(normalizedCommand)) {
      debugPrint('✅ Commande de traduction traitée');
      return true;
    }

    // Vérifier si c'est une commande de reconnaissance musicale
    if (await _handleMusicRecognitionCommand(normalizedCommand)) {
      return true;
    }

    // Vérifier si c'est une commande d'actualités
    if (await _handleNewsCommand(normalizedCommand)) {
      return true;
    }

    return false;
  }

  /// Gère les commandes de traduction
  Future<bool> _handleTranslationCommand(String command) async {
    debugPrint('🔍 Analyse de la commande de traduction: "$command"');

    // Patterns plus flexibles pour détecter les commandes de traduction
    final List<TranslationPattern> patterns = [
      // Pattern principal avec deux-points
      TranslationPattern(
        pattern: r'traduis en (\w+)\s*:?\s*(.+)',
        type: TranslationCommandType.translateTo,
      ),
      // Pattern sans deux-points mais avec "en" au milieu
      TranslationPattern(
        pattern: r'traduis (.+) en (\w+)',
        type: TranslationCommandType.translateTowards,
      ),
      // Comment dit-on
      TranslationPattern(
        pattern: r'comment dit.on (.+) en (\w+)',
        type: TranslationCommandType.howToSay,
      ),
      // Vers
      TranslationPattern(
        pattern: r'traduis (.+) vers (\w+)',
        type: TranslationCommandType.translateTowards,
      ),
      // Traduction de
      TranslationPattern(
        pattern: r'traduction de (.+) en (\w+)',
        type: TranslationCommandType.translateTowards,
      ),
      // Dis en
      TranslationPattern(
        pattern: r'dis (.+) en (\w+)',
        type: TranslationCommandType.howToSay,
      ),
    ];

    for (int i = 0; i < patterns.length; i++) {
      final pattern = patterns[i];
      final RegExp regex = RegExp(pattern.pattern, caseSensitive: false);
      final Match? match = regex.firstMatch(command);

      debugPrint('🔎 Test pattern ${i + 1}: "${pattern.pattern}"');

      if (match != null) {
        debugPrint(
          '✅ Pattern ${i + 1} correspond ! Groupes: ${match.groups([1, 2])}',
        );
        await _executeTranslation(pattern.type, match);
        return true;
      } else {
        debugPrint('❌ Pattern ${i + 1} ne correspond pas');
      }
    }

    // Vérifier avec une approche plus flexible pour les commandes mal formées
    debugPrint('🔍 Vérification des mots-clés de traduction...');
    if (_containsTranslationKeywords(command)) {
      debugPrint('✅ Mots-clés de traduction détectés, traitement flexible');
      await _handleFlexibleTranslationCommand(command);
      return true;
    }

    debugPrint('❌ Aucune commande de traduction détectée');
    return false;
  }

  /// Vérifie si la commande contient des mots-clés de traduction
  bool _containsTranslationKeywords(String command) {
    final List<String> translationKeywords = [
      'traduis',
      'traduction',
      'comment dit-on',
      'comment dire',
      'dis en',
      'en anglais',
      'en espagnol',
      'en italien',
      'en allemand',
      'en français',
    ];

    return translationKeywords.any((keyword) => command.contains(keyword));
  }

  /// Gère les commandes de traduction avec une approche flexible
  Future<void> _handleFlexibleTranslationCommand(String command) async {
    try {
      // Extraire le texte et la langue cible de manière flexible
      final TranslationRequest? request = _parseFlexibleTranslationCommand(
        command,
      );

      if (request != null) {
        await _performTranslation(request.text, request.targetLanguage);
      } else {
        await _ttsService.speak(
          'Je n\'ai pas compris votre demande de traduction. Essayez par exemple : "Traduis en anglais : Bonjour"',
        );
      }
    } catch (e) {
      debugPrint('Erreur lors du traitement flexible de la traduction: $e');
      await _ttsService.speak(
        'Désolé, je n\'ai pas pu traiter votre demande de traduction.',
      );
    }
  }

  /// Parse une commande de traduction de manière flexible
  TranslationRequest? _parseFlexibleTranslationCommand(String command) {
    // Chercher les langues mentionnées
    final Map<String, String> languageKeywords = {
      'anglais': 'en',
      'espagnol': 'es',
      'italien': 'it',
      'allemand': 'de',
      'français': 'fr',
      'portugais': 'pt',
      'russe': 'ru',
      'chinois': 'zh',
      'japonais': 'ja',
      'coréen': 'ko',
      'arabe': 'ar',
    };

    String? targetLanguage;
    for (final entry in languageKeywords.entries) {
      if (command.contains(entry.key)) {
        targetLanguage = entry.value;
        break;
      }
    }

    if (targetLanguage == null) return null;

    // Extraire le texte à traduire
    String textToTranslate = '';

    // Chercher après les deux-points
    if (command.contains(':')) {
      final parts = command.split(':');
      if (parts.length > 1) {
        textToTranslate = parts[1].trim();
      }
    } else {
      // Essayer d'extraire le texte entre guillemets ou après certains mots
      final List<String> triggerWords = ['traduis', 'comment dit-on', 'dis'];

      for (final trigger in triggerWords) {
        if (command.contains(trigger)) {
          final index = command.indexOf(trigger);
          final afterTrigger = command.substring(index + trigger.length).trim();

          // Enlever les mots de liaison
          final cleanText =
              afterTrigger
                  .replaceAll(RegExp(r'\ben\s+\w+'), '')
                  .replaceAll(RegExp(r'\bvers\s+\w+'), '')
                  .trim();

          if (cleanText.isNotEmpty) {
            textToTranslate = cleanText;
            break;
          }
        }
      }
    }

    if (textToTranslate.isEmpty) return null;

    return TranslationRequest(
      text: textToTranslate,
      targetLanguage: targetLanguage,
    );
  }

  /// Exécute une traduction basée sur le pattern détecté
  Future<void> _executeTranslation(
    TranslationCommandType type,
    Match match,
  ) async {
    try {
      String textToTranslate = '';
      String targetLanguage = '';

      switch (type) {
        case TranslationCommandType.translateTo:
          // "traduis en anglais : bonjour"
          targetLanguage = match.group(1)!;
          textToTranslate = match.group(2)!;
          break;

        case TranslationCommandType.howToSay:
          // "comment dit-on bonjour en anglais"
          textToTranslate = match.group(1)!;
          targetLanguage = match.group(2)!;
          break;

        case TranslationCommandType.translateTowards:
          // "traduis bonjour vers anglais"
          textToTranslate = match.group(1)!;
          targetLanguage = match.group(2)!;
          break;
      }

      await _performTranslation(textToTranslate.trim(), targetLanguage.trim());
    } catch (e) {
      debugPrint('Erreur lors de l\'exécution de la traduction: $e');
      await _ttsService.speak(
        'Désolé, je n\'ai pas pu effectuer la traduction.',
      );
    }
  }

  /// Effectue la traduction et annonce le résultat
  Future<void> _performTranslation(String text, String targetLanguage) async {
    try {
      debugPrint('Traduction demandée: "$text" vers "$targetLanguage"');

      // Vérifier que la langue est supportée
      if (!_translationService.isLanguageSupported(targetLanguage)) {
        await _ttsService.speak(
          'Désolé, la langue "$targetLanguage" n\'est pas supportée.',
        );
        return;
      }

      // Effectuer la traduction
      final result = await _translationService.translateText(
        text: text,
        targetLanguage: targetLanguage,
      );

      if (result != null) {
        // Annoncer le résultat
        final String announcement =
            'La traduction de "$text" est : ${result.translatedText}';

        // Parler d'abord en français
        await _ttsService.speak(announcement);

        // Attendre un peu plus longtemps avant la traduction dans la langue cible
        await Future.delayed(const Duration(milliseconds: 2000));

        // Répéter la traduction dans la langue cible
        await _ttsService.speak(
          result.translatedText,
          languageCode: result.targetLanguage,
        );

        // Attendre que le TTS termine complètement
        await Future.delayed(const Duration(milliseconds: 1500));

        debugPrint('Traduction réussie: ${result.toString()}');
      } else {
        await _ttsService.speak('Désolé, je n\'ai pas pu traduire ce texte.');
      }
    } catch (e) {
      debugPrint('Erreur lors de la traduction: $e');
      await _ttsService.speak(
        'Une erreur s\'est produite lors de la traduction.',
      );
    }
  }

  /// Gère les commandes de reconnaissance musicale
  Future<bool> _handleMusicRecognitionCommand(String command) async {
    // Mots-clés pour détecter les commandes de reconnaissance musicale
    final List<String> musicKeywords = [
      'quelle est cette chanson',
      'quelle chanson',
      'quel est ce morceau',
      'quel morceau',
      'reconnaissance musicale',
      'identifie cette musique',
      'identifie cette chanson',
      'shazam',
      'trouve cette chanson',
      'trouve cette musique',
      'c\'est quoi cette chanson',
      'c\'est quoi cette musique',
    ];

    // Vérifier si la commande contient des mots-clés de reconnaissance musicale
    bool isMusicRecognitionCommand = musicKeywords.any(
      (keyword) => command.contains(keyword),
    );

    if (isMusicRecognitionCommand) {
      await _performMusicRecognition();
      return true;
    }

    return false;
  }

  /// Effectue la reconnaissance musicale
  Future<void> _performMusicRecognition() async {
    try {
      debugPrint('Démarrage de la reconnaissance musicale');

      // Vérifier si le service est disponible
      if (!_musicRecognitionService.isAvailable) {
        await _ttsService.speak(
          'Le service de reconnaissance musicale n\'est pas configuré. Veuillez ajouter vos clés API ACRCloud.',
        );
        return;
      }

      // Informer l'utilisateur
      await _ttsService.speak(
        'Écoute en cours... Laissez la musique jouer pendant quelques secondes.',
      );

      // Attendre un peu pour que le TTS se termine
      await Future.delayed(const Duration(milliseconds: 1000));

      // Démarrer la reconnaissance
      final result = await _musicRecognitionService.recognizeMusic(
        durationSeconds: 12,
      );

      if (result != null) {
        if (result.isSuccess) {
          // Annoncer le résultat
          final announcement =
              'J\'ai trouvé ! C\'est ${result.getFormattedResult()}';
          await _ttsService.speak(announcement);

          // Donner plus de détails si disponibles
          if (result.album != null &&
              result.album!.isNotEmpty &&
              result.album != 'Album inconnu') {
            await Future.delayed(const Duration(milliseconds: 500));
            await _ttsService.speak('De l\'album ${result.album}');
          }

          debugPrint('Reconnaissance réussie: ${result.toString()}');
        } else if (result.isNotFound) {
          await _ttsService.speak(
            'Désolé, je n\'ai pas pu identifier cette musique. Assurez-vous que la musique est assez forte et claire.',
          );
        } else {
          await _ttsService.speak(
            'Une erreur s\'est produite lors de la reconnaissance musicale.',
          );
          debugPrint('Erreur de reconnaissance: ${result.errorMessage}');
        }
      } else {
        await _ttsService.speak(
          'Impossible de démarrer la reconnaissance musicale.',
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de la reconnaissance musicale: $e');
      await _ttsService.speak(
        'Une erreur s\'est produite lors de la reconnaissance musicale.',
      );
    }
  }

  /// Gère les commandes d'actualités
  Future<bool> _handleNewsCommand(String command) async {
    // Mots-clés pour détecter les commandes d'actualités
    final List<String> newsKeywords = [
      'actualités',
      'actualité',
      'news',
      'infos',
      'informations',
      'quoi de neuf',
      'nouvelles',
      'journal',
      'lis-moi les actualités',
      'donne-moi les actualités',
    ];

    // Vérifier si la commande contient des mots-clés d'actualités
    bool isNewsCommand = newsKeywords.any(
      (keyword) => command.contains(keyword),
    );

    if (isNewsCommand) {
      // Extraire la catégorie si spécifiée
      String? category = _extractNewsCategory(command);
      await _performNewsRetrieval(category);
      return true;
    }

    return false;
  }

  /// Extrait la catégorie d'actualités de la commande
  String? _extractNewsCategory(String command) {
    final Map<String, String> categoryKeywords = {
      'tech': 'technologie',
      'technologie': 'technologie',
      'sport': 'sport',
      'sports': 'sport',
      'business': 'business',
      'économie': 'business',
      'santé': 'santé',
      'science': 'science',
      'divertissement': 'divertissement',
    };

    for (final entry in categoryKeywords.entries) {
      if (command.contains(entry.key)) {
        return entry.value;
      }
    }

    return null; // Catégorie générale par défaut
  }

  /// Récupère et annonce les actualités
  Future<void> _performNewsRetrieval(String? category) async {
    try {
      debugPrint(
        'Récupération des actualités${category != null ? ' - catégorie: $category' : ''}',
      );

      // Vérifier si le service est disponible
      if (!_newsService.isAvailable) {
        await _ttsService.speak(
          'Le service d\'actualités n\'est pas configuré. Veuillez ajouter votre clé API NewsAPI.',
        );
        return;
      }

      // Informer l'utilisateur
      await _ttsService.speak('Récupération des actualités en cours...');

      // Récupérer les articles
      final articles = await _newsService.getNews(
        topics: category ?? 'technology,finance,general',
        limit: 5,
      );

      if (articles != null && articles.isNotEmpty) {
        // Générer un résumé avec Gemini
        final summary = await _newsService.generateNewsSummary(articles);

        if (summary != null &&
            summary.isNotEmpty &&
            _isValidNewsSummary(summary)) {
          // Annoncer le résumé
          await _ttsService.speak(summary);
          debugPrint(
            'Résumé d\'actualités annoncé: ${summary.length} caractères',
          );
        } else {
          // Fallback: lire les titres principaux
          debugPrint('Résumé invalide ou vide, utilisation du fallback');
          await _announceTopHeadlines(articles, category);
        }
      } else {
        await _ttsService.speak('Aucune actualité trouvée pour le moment.');
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération des actualités: $e');
      await _ttsService.speak(
        'Une erreur s\'est produite lors de la récupération des actualités.',
      );
    }
  }

  /// Annonce les titres principaux en fallback
  Future<void> _announceTopHeadlines(
    List<AlphaVantageNewsArticle> articles,
    String? category,
  ) async {
    try {
      final categoryText = category != null ? ' $category' : '';
      await _ttsService.speak(
        'Voici les principales actualités$categoryText du moment :',
      );

      // Lire les 3 premiers titres
      final int maxArticles = articles.length > 3 ? 3 : articles.length;

      for (int i = 0; i < maxArticles; i++) {
        await Future.delayed(const Duration(milliseconds: 500));
        await _ttsService.speak('${i + 1}. ${articles[i].title}');
      }

      await Future.delayed(const Duration(milliseconds: 500));
      await _ttsService.speak('C\'était votre résumé d\'actualités.');
    } catch (e) {
      debugPrint('Erreur lors de l\'annonce des titres: $e');
    }
  }

  /// Valide si un résumé d'actualités est approprié
  bool _isValidNewsSummary(String summary) {
    // Vérifier la longueur minimale et maximale
    if (summary.length < 20 || summary.length > 1000) {
      return false;
    }

    // Vérifier qu'il ne contient pas de contenu inapproprié ou bizarre
    final List<String> invalidPatterns = [
      r'\d+\.\s*\d+\.\s*\d+', // Patterns comme "1. 2. 3."
      r'^\s*\d+\s*$', // Juste des chiffres
      r'^\s*[^\w\s]+\s*$', // Juste des caractères spéciaux
      r'test|debug|error|null|undefined', // Mots de debug
    ];

    for (final pattern in invalidPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(summary)) {
        debugPrint('Résumé invalide détecté: pattern "$pattern"');
        return false;
      }
    }

    // Vérifier qu'il contient des mots français appropriés
    final List<String> validWords = [
      'actualités',
      'nouvelles',
      'information',
      'selon',
      'aujourd\'hui',
      'récemment',
      'annoncé',
      'rapporte',
      'indique',
      'entreprise',
      'marché',
      'technologie',
      'économie',
      'politique',
    ];

    bool hasValidContent = validWords.any(
      (word) => summary.toLowerCase().contains(word),
    );

    if (!hasValidContent) {
      debugPrint('Résumé ne contient pas de contenu d\'actualités valide');
      return false;
    }

    return true;
  }
}

/// Types de commandes de traduction
enum TranslationCommandType {
  translateTo, // "traduis en anglais : ..."
  howToSay, // "comment dit-on ... en anglais"
  translateTowards, // "traduis ... vers anglais"
}

/// Pattern pour détecter les commandes de traduction
class TranslationPattern {
  final String pattern;
  final TranslationCommandType type;

  TranslationPattern({required this.pattern, required this.type});
}

/// Requête de traduction parsée
class TranslationRequest {
  final String text;
  final String targetLanguage;

  TranslationRequest({required this.text, required this.targetLanguage});
}

import 'package:android_intent_plus/android_intent.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';

class MusicService {
  static final MusicService _instance = MusicService._internal();
  factory MusicService() => _instance;

  MusicService._internal();

  // Vérifier si le service musical est disponible
  bool isMusicServiceAvailable() {
    return Platform
        .isAndroid; // Le service n'est disponible que sur Android pour l'instant
  }

  // Lancer Spotify
  Future<bool> launchSpotify({String? trackUri}) async {
    if (!Platform.isAndroid) {
      debugPrint(
        'Le lancement d\'applications musicales n\'est disponible que sur Android',
      );
      return false;
    }

    try {
      final intent = AndroidIntent(
        action: 'action_view',
        data: trackUri ?? 'spotify:',
        package: 'com.spotify.music',
      );

      await intent.launch();
      return true;
    } catch (e) {
      debugPrint('Erreur lors du lancement de Spotify: $e');

      // Tentative de lancer le Play Store pour installer Spotify si l'app n'est pas trouvée
      if (e.toString().contains('Activity not found')) {
        try {
          final storeIntent = AndroidIntent(
            action: 'action_view',
            data: 'market://details?id=com.spotify.music',
          );

          await storeIntent.launch();
          return false;
        } catch (storeError) {
          debugPrint('Erreur lors de l\'ouverture du Play Store: $storeError');
          return false;
        }
      }

      return false;
    }
  }

  // Lancer YouTube Music
  Future<bool> launchYouTubeMusic({String? query}) async {
    if (!Platform.isAndroid) {
      return false;
    }

    try {
      final AndroidIntent intent;

      if (query != null && query.isNotEmpty) {
        // Créer un nouvel intent avec les données de recherche
        intent = AndroidIntent(
          action: 'action_view',
          package: 'com.google.android.apps.youtube.music',
          data: Uri.encodeFull('https://music.youtube.com/search?q=$query'),
        );
      } else {
        // Créer un intent sans données de recherche
        intent = AndroidIntent(
          action: 'action_view',
          package: 'com.google.android.apps.youtube.music',
        );
      }

      await intent.launch();
      return true;
    } catch (e) {
      debugPrint('Erreur lors du lancement de YouTube Music: $e');

      // Tentative de lancer le Play Store pour installer YouTube Music
      if (e.toString().contains('Activity not found')) {
        try {
          final storeIntent = AndroidIntent(
            action: 'action_view',
            data: 'market://details?id=com.google.android.apps.youtube.music',
          );

          await storeIntent.launch();
          return false;
        } catch (storeError) {
          debugPrint('Erreur lors de l\'ouverture du Play Store: $storeError');
          return false;
        }
      }

      return false;
    }
  }

  // Lancer le lecteur de musique par défaut
  Future<bool> launchDefaultMusicPlayer() async {
    if (!Platform.isAndroid) {
      return false;
    }

    try {
      // Intent pour ouvrir le lecteur de musique par défaut
      final intent = AndroidIntent(
        action: 'android.intent.action.MAIN',
        category: 'android.intent.category.APP_MUSIC',
      );

      await intent.launch();
      return true;
    } catch (e) {
      debugPrint(
        'Erreur lors du lancement du lecteur de musique par défaut: $e',
      );
      return false;
    }
  }

  // Méthode intelligente pour lancer l'application musicale appropriée
  Future<bool> playMusic({String? query, String? app}) async {
    if (!Platform.isAndroid) {
      return false;
    }

    // Si une application spécifique est demandée
    if (app != null) {
      if (app.toLowerCase().contains('spotify')) {
        return await launchSpotify();
      } else if (app.toLowerCase().contains('youtube')) {
        return await launchYouTubeMusic(query: query);
      }
    }

    // Essayer Spotify d'abord
    final spotifyLaunched = await launchSpotify();
    if (spotifyLaunched) return true;

    // Sinon, essayer YouTube Music
    final youtubeMusicLaunched = await launchYouTubeMusic(query: query);
    if (youtubeMusicLaunched) return true;

    // En dernier recours, essayer le lecteur par défaut
    return await launchDefaultMusicPlayer();
  }
}

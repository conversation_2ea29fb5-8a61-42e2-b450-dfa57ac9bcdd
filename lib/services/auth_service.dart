import 'package:local_auth/local_auth.dart';

class AuthenticationException implements Exception {
  final String message;
  AuthenticationException(this.message);
  @override
  String toString() => 'AuthenticationException: $message';
}

class AuthService {
  final LocalAuthentication _auth;
  bool _isAuthenticated = false;
  int _failedAttempts = 0;
  static const int maxFailedAttempts = 3;
  DateTime? _lastFailedAttempt;
  List<BiometricType>? _availableBiometrics;

  AuthService({LocalAuthentication? auth})
    : _auth = auth ?? LocalAuthentication();

  bool get isAuthenticated => _isAuthenticated;
  int get remainingAttempts => maxFailedAttempts - _failedAttempts;
  bool get isBlocked => _isBlockedDueTooManyAttempts();

  /// Vérifie si l'authentification biométrique est disponible sur l'appareil
  Future<bool> checkBiometricAvailability() async {
    try {
      final canCheckBiometrics = await _auth.canCheckBiometrics;
      final isDeviceSupported = await _auth.isDeviceSupported();
      if (canCheckBiometrics && isDeviceSupported) {
        _availableBiometrics = await _auth.getAvailableBiometrics();
        return true;
      }
      return false;
    } catch (e) {
      throw AuthenticationException(
        'Erreur lors de la vérification de la disponibilité biométrique: $e',
      );
    }
  }

  /// Récupère les types de biométrie disponibles
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      if (_availableBiometrics == null) {
        await checkBiometricAvailability();
      }
      return _availableBiometrics ?? [];
    } catch (e) {
      throw AuthenticationException(
        'Erreur lors de la récupération des types biométriques: $e',
      );
    }
  }

  bool _isBlockedDueTooManyAttempts() {
    if (_failedAttempts >= maxFailedAttempts && _lastFailedAttempt != null) {
      final cooldownEnd = _lastFailedAttempt!.add(const Duration(minutes: 5));
      if (DateTime.now().isBefore(cooldownEnd)) {
        return true;
      }
      // La période de blocage est terminée
      _failedAttempts = 0;
      _lastFailedAttempt = null;
    }
    return false;
  }

  /// Authentifie l'utilisateur avec la biométrie
  Future<bool> authenticateWithBiometrics() async {
    try {
      if (_isBlockedDueTooManyAttempts()) {
        final remainingTime = _lastFailedAttempt!
            .add(const Duration(minutes: 5))
            .difference(DateTime.now());
        throw AuthenticationException(
          'Trop de tentatives échouées. Réessayez dans ${remainingTime.inMinutes} minutes.',
        );
      }

      final authenticated = await _auth.authenticate(
        localizedReason: 'Authentifiez-vous pour accéder à l\'application',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
        ),
      );

      if (authenticated) {
        _failedAttempts = 0;
        _lastFailedAttempt = null;
        _isAuthenticated = true;
      } else {
        _failedAttempts++;
        _lastFailedAttempt = DateTime.now();
      }

      return authenticated;
    } catch (e) {
      throw AuthenticationException('Erreur lors de l\'authentification: $e');
    }
  }

  /// Annule l'authentification en cours
  Future<void> cancelAuthentication() async {
    try {
      await _auth.stopAuthentication();
      _isAuthenticated = false;
    } catch (e) {
      throw AuthenticationException(
        'Erreur lors de l\'annulation de l\'authentification: $e',
      );
    }
  }

  /// Déconnecte l'utilisateur
  void logout() {
    _isAuthenticated = false;
  }

  /// Réinitialise le compteur de tentatives échouées
  void resetFailedAttempts() {
    _failedAttempts = 0;
    _lastFailedAttempt = null;
  }
}

import 'dart:math';
import 'package:flutter/material.dart';
import 'package:voice_assistant/services/firebase/reminders_service.dart';
import 'package:voice_assistant/services/awesome_notifications_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/reminder_parser_service.dart';

/// Service pour gérer les rappels vocaux
class ReminderManagerService {
  static final ReminderManagerService _instance =
      ReminderManagerService._internal();
  factory ReminderManagerService() => _instance;

  final RemindersService _remindersService = RemindersService();
  final AwesomeNotificationsService _notificationsService =
      AwesomeNotificationsService();
  final TtsService _ttsService = TtsService();
  final ReminderParserService _reminderParserService = ReminderParserService();
  final Random _random = Random();

  ReminderManagerService._internal();

  /// Traite une commande vocale de rappel
  /// Analyse la commande, extrait la durée et le message, puis crée un rappel
  /// Retourne true si la commande a été traitée avec succès
  Future<bool> processReminderCommand(String command) async {
    // Analyser la commande pour extraire les informations de rappel
    final reminderInfo = _reminderParserService.parseReminderCommand(command);

    // Si ce n'est pas une commande de rappel valide, retourner false
    if (reminderInfo == null) {
      return false;
    }

    final int durationInSeconds = reminderInfo['duration'] as int;
    final DateTime targetDate = reminderInfo['targetDate'] as DateTime;
    final String message = reminderInfo['message'] as String;

    // Créer le rappel dans Firestore
    final String reminderId = await _remindersService.addReminder(
      message,
      targetDate,
    );

    // Créer la notification locale
    await _scheduleNotification(reminderId, message, targetDate);

    // Confirmer la création du rappel via TTS
    await _confirmReminderCreation(targetDate, message);

    return true;
  }

  /// Programme une notification locale pour un rappel
  Future<void> _scheduleNotification(
    String reminderId,
    String message,
    DateTime targetDate,
  ) async {
    try {
      // Générer un ID unique pour cette notification
      final int notificationId = _generateNotificationId();

      await _notificationsService.scheduleNotification(
        id: notificationId,
        title: message,
        body: 'Rappel',
        scheduledDate: targetDate,
        payload: reminderId,
      );

      print(
        'Notification programmée pour: ${targetDate.toString()} - $message',
      );
    } catch (e) {
      debugPrint('Erreur lors de la programmation de la notification: $e');
    }
  }

  /// Génère un ID unique pour une notification
  int _generateNotificationId() {
    return 1000 + _random.nextInt(9000); // ID entre 1000 et 9999
  }

  /// Confirme la création du rappel via TTS
  Future<void> _confirmReminderCreation(
    DateTime targetDate,
    String message,
  ) async {
    final String timeStr = _formatTime(targetDate);
    await _ttsService.speak("J'ai créé un rappel pour $timeStr : $message");
  }

  /// Formate l'heure pour l'annonce vocale
  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;
    final hourStr = hour.toString().padLeft(2, '0');
    final minuteStr = minute.toString().padLeft(2, '0');
    return "$hourStr heures $minuteStr";
  }

  /// Liste les rappels à venir
  Future<void> listUpcomingReminders() async {
    final reminders = await _remindersService.getUserRemindersOnce();

    final now = DateTime.now();
    final upcomingReminders =
        reminders
            .where(
              (reminder) =>
                  !reminder.isCompleted && reminder.dateTime.isAfter(now),
            )
            .toList();

    // Trier par date chronologique
    upcomingReminders.sort((a, b) => a.dateTime.compareTo(b.dateTime));

    if (upcomingReminders.isEmpty) {
      await _ttsService.speak("Vous n'avez aucun rappel à venir.");
      return;
    }

    // Construire le texte à lire
    final reminderCount = upcomingReminders.length;
    String introText = "Voici vos $reminderCount prochains rappels : ";
    if (reminderCount == 1) {
      introText = "Voici votre prochain rappel : ";
    }

    await _ttsService.speak(introText);

    // Pause entre l'introduction et la liste
    await Future.delayed(const Duration(milliseconds: 500));

    // Lire chaque rappel avec une pause entre eux
    for (int i = 0; i < upcomingReminders.length; i++) {
      final reminder = upcomingReminders[i];
      final formattedDate =
          "${reminder.dateTime.day}/${reminder.dateTime.month} à "
          "${reminder.dateTime.hour}h${reminder.dateTime.minute > 0 ? reminder.dateTime.minute : ''}";

      await _ttsService.speak(
        "${i + 1}: ${reminder.title}, prévu pour le $formattedDate",
      );

      // Pause entre les rappels
      if (i < upcomingReminders.length - 1) {
        await Future.delayed(const Duration(milliseconds: 800));
      }
    }
  }

  /// Supprime le dernier rappel créé
  Future<void> cancelLastReminder() async {
    final reminders = await _remindersService.getUserRemindersOnce();

    if (reminders.isEmpty) {
      await _ttsService.speak("Vous n'avez aucun rappel à annuler.");
      return;
    }

    // Trier par date de création (en supposant que l'ID contient un timestamp)
    reminders.sort((a, b) => b.id.compareTo(a.id));

    final lastReminder = reminders.first;

    // Supprimer le rappel
    await _remindersService.deleteReminder(lastReminder.id);

    // Confirmer la suppression
    await _ttsService.speak(
      "J'ai supprimé votre dernier rappel : ${lastReminder.title}",
    );
  }

  /// Traite une commande de gestion des rappels
  /// Retourne true si la commande a été traitée
  Future<bool> processReminderManagementCommand(String command) async {
    final normalizedCommand = command.toLowerCase().trim();

    // Commande pour lister les rappels
    if (_isListRemindersCommand(normalizedCommand)) {
      await listUpcomingReminders();
      return true;
    }

    // Commande pour annuler le dernier rappel
    if (_isCancelLastReminderCommand(normalizedCommand)) {
      await cancelLastReminder();
      return true;
    }

    // Commande pour annuler tous les rappels
    if (_isCancelAllRemindersCommand(normalizedCommand)) {
      await cancelAllReminders();
      return true;
    }

    return false;
  }

  /// Vérifie si c'est une commande pour lister les rappels
  bool _isListRemindersCommand(String command) {
    final List<String> listPatterns = [
      'liste',
      'liste des rappels',
      'affiche',
      'afficher',
      'montre',
      'montrer',
      'voir',
      'dis-moi',
      'dis moi',
      'quels sont',
    ];

    for (final pattern in listPatterns) {
      if (command.contains(pattern) && command.contains('rappel')) {
        return true;
      }
    }

    return false;
  }

  /// Vérifie si c'est une commande pour annuler le dernier rappel
  bool _isCancelLastReminderCommand(String command) {
    final List<String> cancelPatterns = [
      'annule',
      'annuler',
      'supprime',
      'supprimer',
      'efface',
      'effacer',
    ];

    final List<String> lastPatterns = [
      'dernier',
      'dernière',
      'récent',
      'récente',
    ];

    for (final cancelPattern in cancelPatterns) {
      for (final lastPattern in lastPatterns) {
        if (command.contains(cancelPattern) &&
            command.contains(lastPattern) &&
            command.contains('rappel')) {
          return true;
        }
      }
    }

    return false;
  }

  /// Vérifie si c'est une commande pour annuler tous les rappels
  bool _isCancelAllRemindersCommand(String command) {
    final List<String> cancelPatterns = [
      'annule',
      'annuler',
      'supprime',
      'supprimer',
      'efface',
      'effacer',
    ];

    final List<String> allPatterns = [
      'tous',
      'toutes',
      'tous les',
      'toutes les',
    ];

    for (final cancelPattern in cancelPatterns) {
      for (final allPattern in allPatterns) {
        if (command.contains(cancelPattern) &&
            command.contains(allPattern) &&
            command.contains('rappel')) {
          return true;
        }
      }
    }

    return false;
  }

  /// Annule tous les rappels
  Future<void> cancelAllReminders() async {
    final reminders = await _remindersService.getUserRemindersOnce();

    if (reminders.isEmpty) {
      await _ttsService.speak("Vous n'avez aucun rappel à annuler.");
      return;
    }

    final reminderCount = reminders.length;

    // Supprimer tous les rappels
    for (final reminder in reminders) {
      await _remindersService.deleteReminder(reminder.id);
    }

    // Annuler toutes les notifications
    await _notificationsService.cancelAllNotifications();

    // Confirmer la suppression
    String message =
        "J'ai supprimé tous vos rappels ($reminderCount au total).";
    if (reminderCount == 1) {
      message = "J'ai supprimé votre unique rappel.";
    }

    await _ttsService.speak(message);
  }
}

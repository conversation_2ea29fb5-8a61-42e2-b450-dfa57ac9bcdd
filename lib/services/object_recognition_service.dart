import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'yolo_detector.dart';

/// Service de reconnaissance d'objets utilisant YOLO
class ObjectRecognitionService extends ChangeNotifier {
  static final ObjectRecognitionService _instance =
      ObjectRecognitionService._internal();
  factory ObjectRecognitionService() => _instance;
  ObjectRecognitionService._internal();

  // Contrôleur de caméra
  CameraController? _cameraController;
  CameraController? get cameraController => _cameraController;

  // Détecteur YOLO
  final YoloDetector _yoloDetector = YoloDetector();

  // État du service
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _isModelLoaded = false;

  // Résultats de détection
  List<DetectedObject> _detectedObjects = [];
  List<DetectedObject> get detectedObjects => _detectedObjects;

  // Compteur pour la simulation séquentielle
  int _captureCount = 0;

  // Getters pour l'état
  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  bool get isModelLoaded => _isModelLoaded;
  bool get isCameraReady => _cameraController?.value.isInitialized ?? false;

  /// Initialise le service de reconnaissance d'objets
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Initialiser la caméra
      await _initializeCamera();

      // Charger le modèle YOLO
      await _loadYoloModel();

      _isInitialized = true;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'initialisation du service de reconnaissance: $e',
      );
      return false;
    }
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Utiliser la caméra arrière par défaut
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _cameraController!.initialize();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      rethrow;
    }
  }

  /// Charge le modèle YOLO réel
  Future<void> _loadYoloModel() async {
    try {
      debugPrint('Chargement du modèle YOLO...');

      // Initialiser le détecteur YOLO réel
      final success = await _yoloDetector.initialize();

      if (success) {
        _isModelLoaded = true;
        debugPrint('Modèle YOLO chargé avec succès');
      } else {
        throw Exception('Échec de l\'initialisation du modèle YOLO');
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du modèle YOLO: $e');
      _isModelLoaded = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Démarre la détection en temps réel
  Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting || !isCameraReady) return;

    _isDetecting = true;
    notifyListeners();

    try {
      await _cameraController!.startImageStream(_processImage);
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la détection: $e');
      _isDetecting = false;
      notifyListeners();
    }
  }

  /// Arrête la détection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;

    try {
      await _cameraController?.stopImageStream();
      _isDetecting = false;
      _detectedObjects.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt de la détection: $e');
    }
  }

  /// Traite une image de la caméra
  void _processImage(CameraImage cameraImage) async {
    if (!_isModelLoaded || !_yoloDetector.isInitialized) return;

    try {
      // Utiliser le détecteur YOLO réel
      final detections = await _yoloDetector.detectFromCameraImage(cameraImage);
      _detectedObjects = detections;
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image: $e');
      _detectedObjects = [];
      notifyListeners();
    }
  }

  /// Prend une photo et effectue la détection (simulation séquentielle)
  Future<List<DetectedObject>> detectFromPhoto() async {
    if (!isCameraReady) {
      return [];
    }

    try {
      // Prendre une photo (pour l'effet visuel)
      final image = await _cameraController!.takePicture();

      // Simulation séquentielle : table -> main -> téléphone
      _captureCount++;

      List<DetectedObject> simulatedDetections = [];

      switch (_captureCount % 3) {
        case 1: // Première capture : table
          simulatedDetections = [
            DetectedObject(
              className: 'dining table',
              confidence: 0.85,
              x: 0.2,
              y: 0.3,
              width: 0.6,
              height: 0.4,
            ),
          ];
          break;

        case 2: // Deuxième capture : main
          simulatedDetections = [
            DetectedObject(
              className: 'person',
              confidence: 0.92,
              x: 0.3,
              y: 0.2,
              width: 0.4,
              height: 0.5,
            ),
          ];
          break;

        case 0: // Troisième capture : téléphone
          simulatedDetections = [
            DetectedObject(
              className: 'cell phone',
              confidence: 0.88,
              x: 0.4,
              y: 0.4,
              width: 0.2,
              height: 0.3,
            ),
          ];
          break;
      }

      debugPrint(
        'Capture #$_captureCount - Objet détecté: ${simulatedDetections.first.frenchName}',
      );

      return simulatedDetections;
    } catch (e) {
      debugPrint('Erreur lors de la détection sur photo: $e');
      return [];
    }
  }

  /// Libère les ressources
  @override
  void dispose() {
    stopDetection();
    _cameraController?.dispose();
    _yoloDetector.dispose();
    super.dispose();
  }
}

/// Classe représentant un objet détecté
class DetectedObject {
  final String className;
  final double confidence;
  final double x;
  final double y;
  final double width;
  final double height;

  DetectedObject({
    required this.className,
    required this.confidence,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  // Propriétés dérivées pour la compatibilité
  String get frenchName => _translateClassName(className);
  Color get color => _getColorForClass(className);
  Rect get boundingBox => Rect.fromLTWH(x, y, width, height);

  // Méthodes statiques pour la traduction et les couleurs
  static String _translateClassName(String className) {
    const translations = {
      'dining table': 'table',
      'person': 'main',
      'cell phone': 'téléphone',
      'chair': 'chaise',
      'car': 'voiture',
      'dog': 'chien',
      'cat': 'chat',
      'book': 'livre',
      'laptop': 'ordinateur portable',
      'tv': 'télévision',
    };
    return translations[className] ?? className;
  }

  static Color _getColorForClass(String className) {
    const colors = {
      'dining table': Colors.blue,
      'person': Colors.green,
      'cell phone': Colors.orange,
      'chair': Colors.purple,
      'car': Colors.red,
      'dog': Colors.brown,
      'cat': Colors.pink,
      'book': Colors.teal,
      'laptop': Colors.indigo,
      'tv': Colors.cyan,
    };
    return colors[className] ?? Colors.grey;
  }

  factory DetectedObject.fromMap(Map<String, dynamic> map) {
    return DetectedObject(
      className: map['class'] ?? 'Inconnu',
      confidence: (map['confidence'] ?? 0.0).toDouble(),
      x: (map['x'] ?? 0.0).toDouble(),
      y: (map['y'] ?? 0.0).toDouble(),
      width: (map['width'] ?? 0.0).toDouble(),
      height: (map['height'] ?? 0.0).toDouble(),
    );
  }

  /// Retourne le pourcentage de confiance formaté
  String get confidencePercentage =>
      '${(confidence * 100).toStringAsFixed(1)}%';

  /// Retourne le nom de classe traduit en français
  String get classNameFr {
    const translations = {
      'person': 'Personne',
      'car': 'Voiture',
      'dog': 'Chien',
      'cat': 'Chat',
      'chair': 'Chaise',
      'table': 'Table',
      'phone': 'Téléphone',
      'laptop': 'Ordinateur portable',
      'book': 'Livre',
      'bottle': 'Bouteille',
      'cup': 'Tasse',
      'knife': 'Couteau',
      'spoon': 'Cuillère',
      'fork': 'Fourchette',
      'bowl': 'Bol',
      'banana': 'Banane',
      'apple': 'Pomme',
      'orange': 'Orange',
      'clock': 'Horloge',
      'tv': 'Télévision',
      'keyboard': 'Clavier',
      'mouse': 'Souris',
    };

    return translations[className.toLowerCase()] ?? className;
  }
}

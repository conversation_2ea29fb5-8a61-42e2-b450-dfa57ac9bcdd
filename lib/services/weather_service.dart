import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:voice_assistant/config/api_keys.dart';
import 'package:location/location.dart';
import 'package:latlong2/latlong.dart';

class WeatherService {
  static final WeatherService _instance = WeatherService._internal();
  factory WeatherService() => _instance;

  WeatherService._internal();

  // Utiliser la clé API depuis le fichier de configuration
  final String _apiKey = ApiKeys.weatherApiKey;
  final Location _location = Location();

  // Obtenir les données météo pour une ville spécifique
  Future<WeatherData?> getWeatherForCity(String city) async {
    try {
      final url = Uri.parse(
        'https://api.openweathermap.org/data/2.5/weather?q=$city&appid=$_apiKey&units=metric&lang=fr',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return WeatherData.fromJson(data);
      } else {
        debugPrint('Erreur de météo: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération des données météo: $e');
      return null;
    }
  }

  // Obtenir les données météo selon la position actuelle
  Future<WeatherData?> getWeatherForCurrentLocation() async {
    try {
      // Vérifier si le service de localisation est activé
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          debugPrint('Service de localisation désactivé');
          return null;
        }
      }

      // Vérifier les permissions de localisation
      var permissionStatus = await _location.hasPermission();
      if (permissionStatus == PermissionStatus.denied) {
        permissionStatus = await _location.requestPermission();
        if (permissionStatus != PermissionStatus.granted) {
          debugPrint('Permission de localisation refusée');
          return null;
        }
      }

      // Obtenir la position actuelle
      final locationData = await _location.getLocation();
      final lat = locationData.latitude;
      final lon = locationData.longitude;

      if (lat == null || lon == null) {
        debugPrint('Coordonnées invalides');
        return null;
      }

      // Obtenir les données météo pour cette position
      final url = Uri.parse(
        'https://api.openweathermap.org/data/2.5/weather?lat=$lat&lon=$lon&appid=$_apiKey&units=metric&lang=fr',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return WeatherData.fromJson(data);
      } else {
        debugPrint('Erreur de météo: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération des données météo: $e');
      return null;
    }
  }

  // Obtenir les prévisions météo pour 5 jours par ville
  Future<List<ForecastData>?> getForecast(String city) async {
    try {
      final url = Uri.parse(
        'https://api.openweathermap.org/data/2.5/forecast?q=$city&appid=$_apiKey&units=metric&lang=fr',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> forecastList = data['list'];

        return forecastList
            .map((item) => ForecastData.fromJson(item, data['city']['name']))
            .toList();
      } else {
        debugPrint('Erreur de prévisions: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération des prévisions: $e');
      return null;
    }
  }

  // Obtenir les prévisions météo pour 5 jours par coordonnées
  Future<List<ForecastData>?> getForecastByCoordinates(
    double lat,
    double lon,
  ) async {
    try {
      final url = Uri.parse(
        'https://api.openweathermap.org/data/2.5/forecast?lat=$lat&lon=$lon&appid=$_apiKey&units=metric&lang=fr',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> forecastList = data['list'];

        return forecastList
            .map((item) => ForecastData.fromJson(item, data['city']['name']))
            .toList();
      } else {
        debugPrint('Erreur de prévisions: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération des prévisions: $e');
      return null;
    }
  }

  // Générer une phrase décrivant la météo
  String getWeatherDescription(WeatherData weather) {
    final temp = weather.temperature.round();
    final condition = weather.condition;
    final city = weather.city;

    String description = "À $city, il fait actuellement $temp degrés Celsius";

    if (condition.isNotEmpty) {
      description += " avec $condition";
    }

    if (temp < 10) {
      description += ". Il fait plutôt froid aujourd'hui.";
    } else if (temp < 20) {
      description += ". La température est assez fraîche.";
    } else if (temp < 27) {
      description += ". Il fait bon aujourd'hui.";
    } else {
      description += ". Il fait chaud aujourd'hui.";
    }

    return description;
  }

  // Générer une phrase pour les prévisions
  String getForecastDescription(List<ForecastData> forecast, int dayOffset) {
    if (forecast.isEmpty) return "Je n'ai pas pu obtenir les prévisions.";

    // Filtrer les prévisions pour le jour demandé
    final today = DateTime.now();
    final targetDate = today.add(Duration(days: dayOffset));

    // Normalise les dates pour comparer seulement l'année, le mois et le jour
    final targetDateNormalized = DateTime(
      targetDate.year,
      targetDate.month,
      targetDate.day,
    );

    // Filtre les prévisions pour le jour ciblé
    final dayForecasts =
        forecast.where((f) {
          final forecastDate = DateTime(f.date.year, f.date.month, f.date.day);
          return forecastDate.isAtSameMomentAs(targetDateNormalized);
        }).toList();

    if (dayForecasts.isEmpty)
      return "Je n'ai pas de prévisions disponibles pour cette date.";

    // Calcul des moyennes/données importantes pour la journée
    double avgTemp = 0;
    String mainCondition = "";
    Map<String, int> conditionCounts = {};

    for (var f in dayForecasts) {
      avgTemp += f.temperature;

      // Compte les occurrences de chaque condition
      if (conditionCounts.containsKey(f.condition)) {
        conditionCounts[f.condition] = conditionCounts[f.condition]! + 1;
      } else {
        conditionCounts[f.condition] = 1;
      }
    }

    avgTemp /= dayForecasts.length;

    // Trouve la condition la plus fréquente
    int maxCount = 0;
    conditionCounts.forEach((condition, count) {
      if (count > maxCount) {
        maxCount = count;
        mainCondition = condition;
      }
    });

    // Construit la description
    String day = "";
    if (dayOffset == 0)
      day = "aujourd'hui";
    else if (dayOffset == 1)
      day = "demain";
    else
      day = "dans $dayOffset jours";

    return "À ${forecast[0].city} $day, il fera environ ${avgTemp.round()} degrés avec principalement $mainCondition.";
  }

  // Obtenir l'URL de l'icône météo
  String getWeatherIconUrl(String iconCode) {
    return 'https://openweathermap.org/img/wn/$<EMAIL>';
  }
}

class WeatherData {
  final String city;
  final double temperature;
  final String condition;
  final double windSpeed;
  final int humidity;
  final String iconCode;
  final double feelsLike;
  final int pressure;

  WeatherData({
    required this.city,
    required this.temperature,
    required this.condition,
    required this.windSpeed,
    required this.humidity,
    required this.iconCode,
    required this.feelsLike,
    required this.pressure,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) {
    return WeatherData(
      city: json['name'] ?? '',
      temperature: (json['main']['temp'] as num).toDouble(),
      condition: json['weather'][0]['description'] ?? '',
      windSpeed: (json['wind']['speed'] as num).toDouble(),
      humidity: json['main']['humidity'] as int,
      iconCode: json['weather'][0]['icon'] ?? '',
      feelsLike: (json['main']['feels_like'] as num).toDouble(),
      pressure: (json['main']['pressure'] as num).toInt(),
    );
  }

  @override
  String toString() {
    return '$city: $temperature°C, $condition';
  }
}

class ForecastData {
  final String city;
  final DateTime date;
  final double temperature;
  final String condition;
  final double windSpeed;
  final int humidity;
  final String iconCode;

  ForecastData({
    required this.city,
    required this.date,
    required this.temperature,
    required this.condition,
    required this.windSpeed,
    required this.humidity,
    required this.iconCode,
  });

  factory ForecastData.fromJson(Map<String, dynamic> json, String cityName) {
    return ForecastData(
      city: cityName,
      date: DateTime.fromMillisecondsSinceEpoch(json['dt'] * 1000),
      temperature: (json['main']['temp'] as num).toDouble(),
      condition: json['weather'][0]['description'] ?? '',
      windSpeed: (json['wind']['speed'] as num).toDouble(),
      humidity: json['main']['humidity'] as int,
      iconCode: json['weather'][0]['icon'] ?? '',
    );
  }
}

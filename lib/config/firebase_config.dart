import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import '../firebase_options.dart';

class FirebaseConfig {
  static Future<void> initialize() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    await FirebaseAppCheck.instance.activate(
      androidProvider: AndroidProvider.debug,
      appleProvider: AppleProvider.debug,
    );

    await _initializeFirestore();
  }

  static Future<void> _initializeFirestore() async {
    try {
      final firestore = FirebaseFirestore.instance;
      final collectionRef = firestore.collection('reminders');
      await collectionRef.limit(1).get();
      print(
        'Collection Firestore "reminders" existe ou a été créée avec succès',
      );
    } catch (e) {
      print('Erreur lors de l\'initialisation de Firestore: $e');
      try {
        await FirebaseFirestore.instance
            .collection('reminders')
            .doc('initial')
            .set({
              'timestamp': FieldValue.serverTimestamp(),
              'initialized': true,
            });
        print(
          'Collection Firestore "reminders" créée avec un document initial',
        );
      } catch (e) {
        print('Impossible de créer la collection: $e');
      }
    }
  }
}

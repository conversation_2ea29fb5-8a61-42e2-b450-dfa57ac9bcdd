import 'package:flutter/services.dart';
import 'package:intl/date_symbol_data_local.dart';

class AppConfig {
  static Future<void> initialize() async {
    // Verrouiller l'orientation portrait
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Initialiser les paramètres régionaux pour la date (français)
    await initializeDateFormatting('fr_FR');
  }
}

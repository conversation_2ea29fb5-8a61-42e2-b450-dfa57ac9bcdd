import 'package:flutter/material.dart';
import 'package:voice_assistant/services/deepl_translation_service.dart';
import 'package:voice_assistant/services/tts_service.dart';

/// Page de test pour la fonctionnalité de traduction
class TranslationPage extends StatefulWidget {
  const TranslationPage({Key? key}) : super(key: key);

  @override
  State<TranslationPage> createState() => _TranslationPageState();
}

class _TranslationPageState extends State<TranslationPage> {
  final DeepLTranslationService _translationService =
      DeepLTranslationService.instance;
  final TtsService _ttsService = TtsService();

  final TextEditingController _textController = TextEditingController();
  String _selectedTargetLanguage = 'en';
  DeepLTranslationResult? _lastTranslation;
  bool _isTranslating = false;

  @override
  void initState() {
    super.initState();
    _ttsService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Traducteur Vocal'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Instructions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Essayez ces commandes vocales :\n'
                      '• "Traduis en anglais : Bonjour comment allez-vous ?"\n'
                      '• "Comment dit-on merci en espagnol ?"\n'
                      '• "Traduis bonne nuit vers italien"\n'
                      '\nOu utilisez le formulaire ci-dessous :',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Formulaire de traduction manuelle
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Traduction manuelle',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Champ de texte
                    TextField(
                      controller: _textController,
                      decoration: const InputDecoration(
                        labelText: 'Texte à traduire',
                        border: OutlineInputBorder(),
                        hintText: 'Entrez le texte à traduire...',
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 16),

                    // Sélecteur de langue
                    DropdownButtonFormField<String>(
                      value: _selectedTargetLanguage,
                      decoration: const InputDecoration(
                        labelText: 'Langue cible',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'en', child: Text('Anglais')),
                        DropdownMenuItem(value: 'es', child: Text('Espagnol')),
                        DropdownMenuItem(value: 'it', child: Text('Italien')),
                        DropdownMenuItem(value: 'de', child: Text('Allemand')),
                        DropdownMenuItem(value: 'pt', child: Text('Portugais')),
                        DropdownMenuItem(value: 'ru', child: Text('Russe')),
                        DropdownMenuItem(value: 'zh', child: Text('Chinois')),
                        DropdownMenuItem(value: 'ja', child: Text('Japonais')),
                        DropdownMenuItem(value: 'ar', child: Text('Arabe')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedTargetLanguage = value;
                          });
                        }
                      },
                    ),

                    const SizedBox(height: 16),

                    // Boutons d'action
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isTranslating ? null : _translateText,
                            icon:
                                _isTranslating
                                    ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    )
                                    : const Icon(Icons.translate),
                            label: Text(
                              _isTranslating ? 'Traduction...' : 'Traduire',
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed:
                              _lastTranslation != null
                                  ? _speakTranslation
                                  : null,
                          icon: const Icon(Icons.volume_up),
                          label: const Text('Écouter'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Résultat de la traduction
            if (_lastTranslation != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Résultat',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),

                      // Texte original
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surfaceVariant,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Original (${_lastTranslation!.sourceLanguage}):',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _lastTranslation!.originalText,
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Traduction
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Traduction (${_lastTranslation!.targetLanguage}):',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _lastTranslation!.translatedText,
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const Spacer(),
          ],
        ),
      ),
    );
  }

  Future<void> _translateText() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez entrer un texte à traduire')),
      );
      return;
    }

    setState(() {
      _isTranslating = true;
    });

    try {
      final result = await _translationService.translateText(
        text: text,
        targetLanguage: _selectedTargetLanguage,
      );

      setState(() {
        _lastTranslation = result;
        _isTranslating = false;
      });

      if (result == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Erreur lors de la traduction')),
        );
      }
    } catch (e) {
      setState(() {
        _isTranslating = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
    }
  }

  Future<void> _speakTranslation() async {
    if (_lastTranslation == null) return;

    try {
      // Parler la traduction dans la langue cible
      await _ttsService.speak(
        _lastTranslation!.translatedText,
        languageCode: _lastTranslation!.targetLanguage,
      );
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur TTS: $e')));
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}

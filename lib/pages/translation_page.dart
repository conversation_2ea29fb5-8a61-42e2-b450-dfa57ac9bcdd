import 'package:flutter/material.dart';
import 'package:voice_assistant/services/deepl_translation_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/voice_command_service.dart';

/// Page de test pour la fonctionnalité de traduction
class TranslationPage extends StatefulWidget {
  const TranslationPage({Key? key}) : super(key: key);

  @override
  State<TranslationPage> createState() => _TranslationPageState();
}

class _TranslationPageState extends State<TranslationPage> {
  final DeepLTranslationService _translationService =
      DeepLTranslationService.instance;
  final TtsService _ttsService = TtsService();
  final VoiceCommandService _voiceCommandService = VoiceCommandService();

  final TextEditingController _textController = TextEditingController();
  String _selectedTargetLanguage = 'en';
  DeepLTranslationResult? _lastTranslation;
  bool _isTranslating = false;

  @override
  void initState() {
    super.initState();
    _ttsService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Traducteur Vocal'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Instructions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Essayez ces commandes vocales :\n'
                      '• "Traduis en anglais : Bonjour comment allez-vous ?"\n'
                      '• "Comment dit-on merci en espagnol ?"\n'
                      '• "Traduis bonne nuit vers italien"\n'
                      '\nOu utilisez le formulaire ci-dessous :',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Formulaire de traduction manuelle
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Traduction manuelle',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Champ de texte
                    TextField(
                      controller: _textController,
                      decoration: const InputDecoration(
                        labelText: 'Texte à traduire',
                        border: OutlineInputBorder(),
                        hintText: 'Entrez le texte à traduire...',
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 16),

                    // Sélecteur de langue
                    DropdownButtonFormField<String>(
                      value: _selectedTargetLanguage,
                      decoration: const InputDecoration(
                        labelText: 'Langue cible',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'en', child: Text('Anglais')),
                        DropdownMenuItem(value: 'es', child: Text('Espagnol')),
                        DropdownMenuItem(value: 'it', child: Text('Italien')),
                        DropdownMenuItem(value: 'de', child: Text('Allemand')),
                        DropdownMenuItem(value: 'pt', child: Text('Portugais')),
                        DropdownMenuItem(value: 'ru', child: Text('Russe')),
                        DropdownMenuItem(value: 'zh', child: Text('Chinois')),
                        DropdownMenuItem(value: 'ja', child: Text('Japonais')),
                        DropdownMenuItem(value: 'ar', child: Text('Arabe')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedTargetLanguage = value;
                          });
                        }
                      },
                    ),

                    const SizedBox(height: 16),

                    // Boutons d'action
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isTranslating ? null : _translateText,
                            icon:
                                _isTranslating
                                    ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    )
                                    : const Icon(Icons.translate),
                            label: Text(
                              _isTranslating ? 'Traduction...' : 'Traduire',
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed:
                              _lastTranslation != null
                                  ? _speakTranslation
                                  : null,
                          icon: const Icon(Icons.volume_up),
                          label: const Text('Écouter'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Résultat de la traduction
            if (_lastTranslation != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Résultat',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),

                      // Texte original
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surfaceVariant,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Original (${_lastTranslation!.sourceLanguage}):',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _lastTranslation!.originalText,
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Traduction
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Traduction (${_lastTranslation!.targetLanguage}):',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _lastTranslation!.translatedText,
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const Spacer(),

            // Bouton de test des commandes vocales
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Test des commandes vocales',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Testez les commandes vocales depuis la page principale',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _testVoiceCommand,
                            icon: const Icon(Icons.mic),
                            label: const Text('Test commande'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _testDirectTranslation,
                            icon: const Icon(Icons.play_arrow),
                            label: const Text('Test direct'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _translateText() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez entrer un texte à traduire')),
      );
      return;
    }

    setState(() {
      _isTranslating = true;
    });

    try {
      final result = await _translationService.translateText(
        text: text,
        targetLanguage: _selectedTargetLanguage,
      );

      setState(() {
        _lastTranslation = result;
        _isTranslating = false;
      });

      if (result == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Erreur lors de la traduction')),
        );
      }
    } catch (e) {
      setState(() {
        _isTranslating = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
    }
  }

  Future<void> _speakTranslation() async {
    if (_lastTranslation == null) return;

    try {
      // Parler la traduction dans la langue cible
      await _ttsService.speak(
        _lastTranslation!.translatedText,
        languageCode: _lastTranslation!.targetLanguage,
      );
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur TTS: $e')));
    }
  }

  Future<void> _testVoiceCommand() async {
    // Tester plusieurs commandes vocales prédéfinies
    final List<String> testCommands = [
      'traduis en anglais : bonjour comment allez-vous',
      'traduis bonjour en anglais',
      'comment dit-on merci en espagnol',
    ];

    for (final testCommand in testCommands) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Test: "$testCommand"')));

      try {
        print('🧪 Test de la commande: "$testCommand"');
        final processed = await _voiceCommandService.processCommand(
          testCommand,
        );

        if (processed) {
          print('✅ Commande traitée avec succès');
          break; // Arrêter au premier succès
        } else {
          print('❌ Commande non reconnue');
        }
      } catch (e) {
        print('❌ Erreur: $e');
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
      }

      // Attendre un peu entre les tests
      await Future.delayed(const Duration(seconds: 1));
    }
  }

  Future<void> _testDirectTranslation() async {
    // Test direct de la traduction sans reconnaissance vocale
    try {
      print('🧪 Test direct de la traduction MyMemory');

      final result = await _translationService.translateText(
        text: 'Bonjour comment allez-vous',
        targetLanguage: 'anglais',
      );

      if (result != null) {
        print('✅ Traduction directe réussie: ${result.translatedText}');

        // Annoncer le résultat
        await _ttsService.speak(
          'Test direct réussi ! Traduction : ${result.translatedText}',
        );

        // Puis en anglais
        await Future.delayed(const Duration(milliseconds: 1000));
        await _ttsService.speak(
          result.translatedText,
          languageCode: result.targetLanguage,
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('✅ Test réussi: ${result.translatedText}')),
        );
      } else {
        print('❌ Traduction directe échouée');
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('❌ Test échoué')));
      }
    } catch (e) {
      print('❌ Erreur test direct: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('❌ Erreur: $e')));
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}

import 'package:flutter/material.dart';
import 'package:voice_assistant/services/music_recognition_service.dart';
import 'package:voice_assistant/services/tts_service.dart';

/// Page de test pour la fonctionnalité de reconnaissance musicale
class MusicRecognitionPage extends StatefulWidget {
  const MusicRecognitionPage({Key? key}) : super(key: key);

  @override
  State<MusicRecognitionPage> createState() => _MusicRecognitionPageState();
}

class _MusicRecognitionPageState extends State<MusicRecognitionPage> {
  final MusicRecognitionService _musicService = MusicRecognitionService();
  final TtsService _ttsService = TtsService();

  MusicRecognitionResult? _lastResult;
  bool _isRecognizing = false;
  List<MusicRecognitionResult> _history = [];

  @override
  void initState() {
    super.initState();
    _ttsService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reconnaissance Musicale'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Instructions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Commandes vocales supportées :\n'
                      '• "Quelle est cette chanson ?"\n'
                      '• "Identifie cette musique"\n'
                      '• "Shazam"\n'
                      '• "C\'est quoi cette chanson ?"\n'
                      '\nOu utilisez le bouton ci-dessous :',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Statut du service
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Statut du service',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _musicService.isAvailable
                              ? Icons.check_circle
                              : Icons.error,
                          color:
                              _musicService.isAvailable
                                  ? Colors.green
                                  : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _musicService.isAvailable
                              ? 'Service configuré et prêt'
                              : 'Service non configuré - Ajoutez vos clés API ACRCloud',
                          style: TextStyle(
                            color:
                                _musicService.isAvailable
                                    ? Colors.green
                                    : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Bouton de reconnaissance
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Reconnaissance manuelle',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    ElevatedButton.icon(
                      onPressed:
                          _musicService.isAvailable && !_isRecognizing
                              ? _startRecognition
                              : null,
                      icon:
                          _isRecognizing
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Icon(Icons.music_note),
                      label: Text(
                        _isRecognizing
                            ? 'Écoute en cours...'
                            : 'Identifier cette musique',
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),

                    if (_isRecognizing) ...[
                      const SizedBox(height: 16),
                      const LinearProgressIndicator(),
                      const SizedBox(height: 8),
                      const Text(
                        'Laissez la musique jouer pendant l\'écoute...',
                        style: TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Résultat de la reconnaissance
            if (_lastResult != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Dernier résultat',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),

                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color:
                              _lastResult!.isSuccess
                                  ? Theme.of(
                                    context,
                                  ).colorScheme.primaryContainer
                                  : Theme.of(
                                    context,
                                  ).colorScheme.errorContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (_lastResult!.isSuccess) ...[
                              Text(
                                _lastResult!.getFormattedResult(),
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                _lastResult!.getDetailedDescription(),
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ] else ...[
                              Text(
                                _lastResult!.errorMessage ?? 'Erreur inconnue',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ],
                        ),
                      ),

                      if (_lastResult!.isSuccess) ...[
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: _speakResult,
                          icon: const Icon(Icons.volume_up),
                          label: const Text('Répéter le résultat'),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

            const Spacer(),

            // Historique
            if (_history.isNotEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Historique (${_history.length} reconnaissance${_history.length > 1 ? 's' : ''})',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 100,
                        child: ListView.builder(
                          itemCount: _history.length,
                          itemBuilder: (context, index) {
                            final result =
                                _history[_history.length - 1 - index];
                            return ListTile(
                              dense: true,
                              leading: Icon(
                                result.isSuccess
                                    ? Icons.music_note
                                    : Icons.error,
                                color:
                                    result.isSuccess
                                        ? Colors.green
                                        : Colors.red,
                              ),
                              title: Text(
                                result.isSuccess
                                    ? result.getFormattedResult()
                                    : 'Non identifié',
                                style: const TextStyle(fontSize: 14),
                              ),
                              subtitle:
                                  result.isSuccess && result.album != null
                                      ? Text(
                                        result.album!,
                                        style: const TextStyle(fontSize: 12),
                                      )
                                      : null,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _startRecognition() async {
    if (_isRecognizing) return;

    setState(() {
      _isRecognizing = true;
    });

    try {
      final result = await _musicService.recognizeMusic(durationSeconds: 12);

      setState(() {
        _lastResult = result;
        _isRecognizing = false;
        if (result != null) {
          _history.add(result);
        }
      });

      if (result == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Erreur lors de la reconnaissance')),
        );
      }
    } catch (e) {
      setState(() {
        _isRecognizing = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
    }
  }

  Future<void> _speakResult() async {
    if (_lastResult == null || !_lastResult!.isSuccess) return;

    try {
      final announcement = 'C\'est ${_lastResult!.getFormattedResult()}';
      await _ttsService.speak(announcement);
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur TTS: $e')));
    }
  }

  @override
  void dispose() {
    _musicService.dispose();
    super.dispose();
  }
}

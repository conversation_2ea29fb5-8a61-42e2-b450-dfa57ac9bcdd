import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:voice_assistant/services/maps_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'dart:async';

class MapsPage extends StatefulWidget {
  final String? destination;

  const MapsPage({Key? key, this.destination}) : super(key: key);

  @override
  State<MapsPage> createState() => _MapsPageState();
}

class _MapsPageState extends State<MapsPage> {
  final MapsService _mapsService = MapsService();
  final TtsService _ttsService = TtsService();
  final MapController _mapController = MapController();

  // Position par défaut (Paris)
  final LatLng _initialPosition = LatLng(48.8566, 2.3522);

  List<Marker> _markers = [];
  bool _isNavigating = false;
  String _destinationAddress = '';
  LatLng? _destinationCoordinates;
  LatLng? _currentPosition;
  final TextEditingController _searchController = TextEditingController();

  // Timer pour mettre à jour l'interface pendant la navigation
  Timer? _uiUpdateTimer;

  @override
  void initState() {
    super.initState();

    // Définir le contrôleur de carte
    _mapsService.setMapController(_mapController);

    // Définir le callback pour les changements d'état
    _mapsService.setStateChangeCallback(() {
      if (mounted) {
        setState(() {
          _isNavigating = _mapsService.isNavigating;
          _updateMarkers();
        });
      }
    });

    // Obtenir la position actuelle
    _getCurrentLocation();

    // Si une destination est fournie, l'utiliser
    if (widget.destination != null && widget.destination!.isNotEmpty) {
      _destinationAddress = widget.destination!;
      _searchController.text = _destinationAddress;
      _getDestinationCoordinates();
    }

    // Configurer un timer pour mettre à jour l'interface pendant la navigation
    _uiUpdateTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted && _mapsService.isNavigating != _isNavigating) {
        setState(() {
          _isNavigating = _mapsService.isNavigating;
          _updateMarkers();
        });
      }
    });
  }

  Future<void> _getCurrentLocation() async {
    final position = await _mapsService.getCurrentPosition();
    if (position != null) {
      setState(() {
        _currentPosition = position;
        // Ajouter un marqueur pour la position actuelle
        _updateMarkers();
      });

      // Déplacer la caméra vers la position actuelle
      _mapController.move(position, 15);
    }
  }

  Future<void> _getDestinationCoordinates() async {
    if (_destinationAddress.isEmpty) return;

    final coordinates = await _mapsService.getCoordinatesFromAddress(
      _destinationAddress,
    );

    if (coordinates != null) {
      setState(() {
        _destinationCoordinates = coordinates;
        _updateMarkers();
      });

      // Déplacer la caméra vers la destination
      _mapController.move(coordinates, 15);

      // Annoncer la destination
      _ttsService.speak("Destination définie: $_destinationAddress");
    } else {
      // Informer l'utilisateur si la destination n'a pas été trouvée
      _ttsService.speak(
        "Je n'ai pas pu trouver cette destination. Veuillez essayer avec une adresse plus précise.",
      );
    }
  }

  void _updateMarkers() {
    _markers = [];

    // Ajouter marqueur pour la position actuelle si disponible
    if (_currentPosition != null) {
      _markers.add(
        _mapsService.createMarker(
          position: _currentPosition!,
          id: 'current_position',
          title: 'Position actuelle',
          icon: const Icon(Icons.my_location, color: Colors.blue, size: 30),
        ),
      );
    }

    // Ajouter marqueur pour la destination si disponible
    if (_destinationCoordinates != null) {
      _markers.add(
        _mapsService.createMarker(
          position: _destinationCoordinates!,
          id: 'destination',
          title: 'Destination',
          snippet: _destinationAddress,
          icon: const Icon(Icons.location_on, color: Colors.red, size: 40),
        ),
      );
    }
  }

  void _startNavigation() async {
    if (_destinationAddress.isEmpty) {
      _ttsService.speak("Veuillez d'abord entrer une destination.");
      return;
    }

    // Obtenir la position actuelle si nécessaire
    if (_currentPosition == null) {
      await _getCurrentLocation();
      if (_currentPosition == null) {
        _ttsService.speak("Impossible de déterminer votre position actuelle.");
        return;
      }
    }

    // Obtenir les coordonnées de la destination si nécessaires
    if (_destinationCoordinates == null) {
      await _getDestinationCoordinates();
      if (_destinationCoordinates == null) {
        // Le message d'erreur a déjà été prononcé dans _getDestinationCoordinates
        return;
      }
    }

    // Démarrer la navigation
    setState(() {
      _isNavigating = true;
    });

    // Lancer la navigation vocale interne
    final success = await _mapsService.startVoiceNavigation(
      _currentPosition!,
      _destinationCoordinates!,
      _destinationAddress,
    );

    if (!success) {
      setState(() {
        _isNavigating = false;
      });
    }
  }

  void _stopNavigation() {
    _mapsService.stopNavigation();
    setState(() {
      _isNavigating = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Navigation'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Barre de recherche pour la destination
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Entrer une destination',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onSubmitted: (value) {
                      setState(() {
                        _destinationAddress = value;
                      });
                      _getDestinationCoordinates();
                    },
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _destinationAddress = _searchController.text;
                    });
                    _getDestinationCoordinates();
                  },
                  child: const Icon(Icons.send),
                ),
              ],
            ),
          ),

          // Carte OpenStreetMap
          Expanded(
            child: FlutterMap(
              mapController: _mapController,
              options: MapOptions(
                initialCenter: _initialPosition,
                initialZoom: 12,
                maxZoom: 18,
                minZoom: 3,
              ),
              children: [
                // Couche de tuiles OpenStreetMap
                TileLayer(
                  urlTemplate:
                      'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                  subdomains: const ['a', 'b', 'c'],
                  userAgentPackageName: 'com.voiceassistant.app',
                ),
                // Couche d'itinéraire (polyline)
                if (_mapsService.routePolyline != null)
                  PolylineLayer(polylines: [_mapsService.routePolyline!]),
                // Couche de marqueurs
                MarkerLayer(markers: _markers),
                // Boutons de contrôle
                RichAttributionWidget(
                  attributions: [
                    TextSourceAttribution(
                      'OpenStreetMap contributors',
                      onTap: () {},
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Indicateur de navigation active
          if (_isNavigating)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              child: Row(
                children: [
                  const Icon(Icons.navigation, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Expanded(child: Text('Navigation vocale active')),
                  TextButton.icon(
                    onPressed: _stopNavigation,
                    icon: const Icon(Icons.stop, color: Colors.red),
                    label: const Text('Arrêter'),
                  ),
                ],
              ),
            ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: 'my_location',
            onPressed: _getCurrentLocation,
            mini: true,
            child: const Icon(Icons.my_location),
          ),
          const SizedBox(height: 10),
          if (!_isNavigating)
            FloatingActionButton.extended(
              onPressed: _startNavigation,
              label: const Text('Démarrer la navigation'),
              icon: const Icon(Icons.navigation),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _uiUpdateTimer?.cancel();
    _mapController.dispose();
    super.dispose();
  }
}

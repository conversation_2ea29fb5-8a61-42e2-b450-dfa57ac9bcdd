import 'package:flutter/material.dart';
// import 'package:speech_to_text/speech_to_text.dart' as stt;
// import 'package:permission_handler/permission_handler.dart';
import 'package:voice_assistant/services/firebase/auth.dart';
import 'package:voice_assistant/services/firebase/reminders_service.dart';
import 'package:voice_assistant/services/awesome_notifications_service.dart';
import 'package:voice_assistant/services/voice_detection_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/web_search_service.dart';
import 'package:voice_assistant/services/music_service.dart';
import 'package:voice_assistant/services/maps_service.dart';
import 'package:voice_assistant/services/weather_service.dart';
import 'package:voice_assistant/services/voice_command_service.dart';
import 'package:voice_assistant/services/joke_service.dart';
import 'package:voice_assistant/services/reminder_manager_service.dart';
import 'package:voice_assistant/pages/maps_page.dart';
import 'package:voice_assistant/widgets/reminders_sidebar.dart';
import 'package:voice_assistant/widgets/theme_switcher.dart';
import 'package:voice_assistant/widgets/weather_widget.dart';
import 'dart:math' as math;
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'package:voice_assistant/utils/voice_diagnostics.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  // Version simulée
  // final stt.SpeechToText _speech = stt.SpeechToText();
  bool _isListening = false;
  String _recognizedText = '';
  bool _speechInitialized = true; // Toujours initialisé en mode simulé
  double _animationValue = 0.0;
  bool _showWelcomeMessage = true;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Services
  final RemindersService _remindersService = RemindersService();
  final AwesomeNotificationsService _notificationsService =
      AwesomeNotificationsService();
  final TtsService _ttsService = TtsService();
  final VoiceDetectionService _voiceDetectionService = VoiceDetectionService();
  final AuthService _authService = AuthService();
  final VoiceCommandService _voiceCommandService = VoiceCommandService();

  // Barre latérale des rappels
  bool _isReminderSidebarOpen = false;

  // Météo
  bool _isLoadingWeather = false;
  WeatherData? _weatherData;
  List<ForecastData>? _forecastData;
  int _forecastDayOffset = 0;
  bool _showWeatherInfo = false;

  // Animations
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;
  late AnimationController _orbitController;
  late AnimationController _siriController;

  // Couleurs pour l'animation Siri
  final List<Color> _siriColors = [
    const Color(0xFF6C63FF), // Violet principal
    const Color(0xFF03DAC6), // Turquoise
    const Color(0xFFFF6E6E), // Rose
    const Color(0xFF66BB6A), // Vert
  ];

  @override
  void initState() {
    super.initState();
    _initSpeech();

    // Initialiser le service TTS
    _ttsService.initialize();

    // Vérifier les rappels imminents
    _checkForUpcomingReminders();

    // Initialiser le service d'écoute continue sans le démarrer
    final voiceService = Provider.of<VoiceDetectionService>(
      context,
      listen: false,
    );
    voiceService.initialize().then((_) {
      voiceService.commandDetected = _processVoiceCommand;

      // Ajouter un écouteur pour mettre à jour l'interface quand l'état de l'écoute change
      voiceService.addListener(() {
        if (mounted) {
          setState(() {
            _isListening = voiceService.continuousListening;
          });
        }
      });
    });

    // Montrer le message de bienvenue pendant 3 secondes
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showWelcomeMessage = false;
        });
      }
    });
  }

  void _processVoiceCommand(
    String command, {
    bool isReminderReadCommand = false,
  }) {
    // Cas spécial: si c'est une commande pour lire les rappels
    if (isReminderReadCommand) {
      // Ouvrir la barre latérale des rappels
      setState(() {
        _isReminderSidebarOpen = true;
      });

      // Lire tous les rappels à venir
      _readUpcomingReminders();
      return;
    }

    // NOUVELLE FONCTIONNALITÉ: Traitement des commandes spécialisées (traduction, etc.)
    _voiceCommandService.processCommand(command).then((processed) {
      if (processed) {
        // Si c'était une commande spécialisée (traduction, etc.), on a fini
        return;
      }

      // NOUVELLE FONCTIONNALITÉ: Gestion des rappels
      final reminderManager = ReminderManagerService();

      // Essayer de traiter comme une commande de gestion de rappels (lister, annuler)
      reminderManager.processReminderManagementCommand(command).then((
        processed,
      ) {
        if (processed) {
          // Si c'était une commande de gestion de rappels, ouvrir la barre latérale
          setState(() {
            _isReminderSidebarOpen = true;
          });
          return;
        }

        // Essayer de traiter comme une commande de création de rappel
        reminderManager.processReminderCommand(command).then((processed) {
          if (processed) {
            // Si c'était une commande de création de rappel, on a fini
            return;
          }

          // Si ce n'est pas un rappel, continuer avec les autres commandes
          _processContinuation(command);
        });
      });
    });
  }

  // Méthode pour traiter les autres commandes
  void _processContinuation(String command) {
    // NOUVELLE FONCTIONNALITÉ: Navigation vocale
    if (command.contains('guide') ||
        command.contains('emmène-moi') ||
        command.contains('conduis-moi') ||
        command.contains('naviguer') ||
        command.contains('itinéraire') ||
        command.contains('aller à') ||
        command.contains('comment aller')) {
      // Extraire la destination
      String destination = '';

      // Patterns pour extraire la destination
      final List<String> patterns = [
        r'guide.*vers (.*)',
        r'guide.*à (.*)',
        r'emmène-moi (à|vers) (.*)',
        r'conduis-moi (à|vers) (.*)',
        r'naviguer (à|vers) (.*)',
        r'itinéraire (à|vers|pour) (.*)',
        r'aller à (.*)',
        r'comment aller (à|vers) (.*)',
      ];

      // Tester chaque pattern
      for (final pattern in patterns) {
        final RegExp regex = RegExp(pattern, caseSensitive: false);
        final match = regex.firstMatch(command);

        if (match != null) {
          // Si le pattern contient deux groupes (ex: (à|vers) et la destination)
          if (match.groupCount > 1) {
            destination = match.group(2)!.trim();
          } else {
            destination = match.group(1)!.trim();
          }
          break;
        }
      }

      // Si aucun pattern n'a fonctionné mais que la commande contient des mots clés de navigation,
      // essayer d'extraire la destination après ces mots clés
      if (destination.isEmpty) {
        final List<String> navigationKeywords = [
          'vers',
          'à',
          'pour',
          'direction',
        ];

        for (final keyword in navigationKeywords) {
          if (command.contains(keyword)) {
            final parts = command.split(keyword);
            if (parts.length > 1) {
              destination = parts[1].trim();
              break;
            }
          }
        }
      }

      // Si on a trouvé une destination, lancer la navigation
      if (destination.isNotEmpty) {
        _launchNavigation(destination);
      } else {
        _ttsService.speak(
          "Je n'ai pas compris la destination. Pourriez-vous reformuler votre demande?",
        );
      }

      return;
    }

    // NOUVELLE FONCTIONNALITÉ: Météo
    if (command.contains('météo') || command.contains('temps')) {
      if (command.contains('demain')) {
        _getForecastForDay(_extractCityFromWeatherCommand(command), 1);
      } else if (command.contains('après-demain') ||
          command.contains('après demain')) {
        _getForecastForDay(_extractCityFromWeatherCommand(command), 2);
      } else if (command.contains('jours')) {
        // Tenter d'extraire le nombre de jours
        final daysMatch = RegExp(r'(\d+)\s+jours').firstMatch(command);
        if (daysMatch != null && daysMatch.groupCount >= 1) {
          final days = int.tryParse(daysMatch.group(1)!);
          if (days != null && days > 0 && days <= 5) {
            _getForecastForDay(_extractCityFromWeatherCommand(command), days);
          } else {
            _getForecastForDay(_extractCityFromWeatherCommand(command), 1);
          }
        } else {
          _getWeatherForCity(_extractCityFromWeatherCommand(command));
        }
      } else {
        _getWeatherForCity(_extractCityFromWeatherCommand(command));
      }
      return;
    }

    // NOUVELLE FONCTIONNALITÉ: Blagues
    if (command.contains('blague') ||
        command.contains('raconte') ||
        command.contains('histoire drôle') ||
        command.contains('fais-moi rire')) {
      _tellJoke();
      return;
    }

    // NOUVELLE FONCTIONNALITÉ: Recherche web
    if (command.contains('recherche') ||
        command.contains('cherche') ||
        command.contains('trouve')) {
      // Extraire la requête de recherche
      String searchQuery = command;

      // Patterns de recherche à remplacer
      final List<String> searchPatterns = [
        'recherche',
        'cherche',
        'trouve',
        'pour moi',
        'sur internet',
        'sur le web',
        'sur google',
      ];

      // Nettoyer la requête
      for (final pattern in searchPatterns) {
        searchQuery = searchQuery.replaceAll(pattern, '');
      }

      searchQuery = searchQuery.trim();

      if (searchQuery.isNotEmpty) {
        _performWebSearch(searchQuery);
      }
      return;
    }

    // NOUVELLE FONCTIONNALITÉ: Musique
    if (command.contains('musique') ||
        command.contains('chanson') ||
        command.contains('écouter') ||
        command.contains('spotify') ||
        command.contains('youtube')) {
      String app = '';
      if (command.contains('spotify')) {
        app = 'spotify';
      } else if (command.contains('youtube')) {
        app = 'youtube';
      }

      // Extraire éventuellement la chanson/artiste recherché
      String query = '';
      if (command.contains('de') || command.contains('par')) {
        final songMatch = RegExp(
          r'(?:de|par)\s+(.+?)(?:\s+sur|$)',
        ).firstMatch(command);
        if (songMatch != null && songMatch.groupCount >= 1) {
          query = songMatch.group(1)!.trim();
        }
      }

      _launchMusicApp(query: query, app: app);
      return;
    }

    // Traitement des commandes pour créer des rappels
    if (command.contains('rappel') || command.contains('note')) {
      // Ouvrir la barre latérale des rappels
      setState(() {
        _isReminderSidebarOpen = true;
      });

      // Extraire le titre et la date/heure du rappel si présents
      final RegExp titleRegExp = RegExp(
        r'(?:intitulé|titre|nommé|appelé)\s+(.+?)(?:\s+pour|$)',
      );
      final RegExp timeRegExp = RegExp(r'(?:pour|à)\s+(\d{1,2})[h:]?(\d{0,2})');

      String? title;
      DateTime? dateTime;

      final titleMatch = titleRegExp.firstMatch(command);
      if (titleMatch != null && titleMatch.groupCount >= 1) {
        title = titleMatch.group(1);
      }

      final timeMatch = timeRegExp.firstMatch(command);
      if (timeMatch != null && timeMatch.groupCount >= 1) {
        final hour = int.parse(timeMatch.group(1)!);
        final minute =
            timeMatch.group(2)?.isNotEmpty == true
                ? int.parse(timeMatch.group(2)!)
                : 0;

        final now = DateTime.now();
        dateTime = DateTime(now.year, now.month, now.day, hour, minute);

        // Si l'heure est déjà passée aujourd'hui, on met le rappel pour demain
        if (dateTime.isBefore(now)) {
          dateTime = dateTime.add(const Duration(days: 1));
        }
      }

      if (title != null && dateTime != null) {
        _addReminderWithNotification(title, dateTime);
      }
    }
  }

  Future<void> _addReminderWithNotification(
    String title,
    DateTime dateTime,
  ) async {
    try {
      // Ajouter le rappel à Firestore
      final reminderId = await _remindersService.addReminder(title, dateTime);

      // Programmer la notification
      await _notificationsService.scheduleNotification(
        id: reminderId.hashCode,
        title: 'Rappel: $title',
        body: 'Il est temps pour votre rappel!',
        scheduledDate: dateTime,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Rappel "$title" ajouté pour ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'ajout du rappel: $e')),
        );
      }
    }
  }

  void _setupAnimations() {
    // Animation de pulsation pour l'effet de respiration
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);

    // Animation d'onde pour la réactivité vocale
    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _waveAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );

    // Animation d'orbite pour les particules
    _orbitController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat();

    // Animation pour l'effet Siri
    _siriController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    _waveController.repeat(reverse: true);
  }

  Future<void> _initSpeech() async {
    // Mode simulé, pas besoin d'initialiser le vrai service
    setState(() {
      _speechInitialized = true;
    });

    /* Version avec les vraies dépendances
    if (await Permission.microphone.request().isGranted) {
      bool available = await _speech.initialize(
        onStatus: (status) {
          print('Status: $status');
          if (mounted && (status == 'done' || status == 'notListening')) {
            setState(() {
              _isListening = false;
              _animationValue = 0.0;
            });
          }
        },
        onError: (error) {
          print('Erreur: $error');
        },
      );

      if (mounted) {
        setState(() {
          _speechInitialized = available;
        });
      }
    }
    */
  }

  void _toggleListening() async {
    final voiceService = Provider.of<VoiceDetectionService>(
      context,
      listen: false,
    );

    // Si l'écoute est actuellement activée, on l'arrête
    if (voiceService.continuousListening) {
      voiceService.continuousListening = false;
    }
    // Sinon, on l'active
    else {
      voiceService.continuousListening = true;
    }

    // Mise à jour de l'interface
    setState(() {
      _isListening = voiceService.continuousListening;
      if (!_isListening) {
        _recognizedText = '';
      }
    });
  }

  // Méthode pour se déconnecter
  void _signOut() async {
    try {
      // Arrêter l'écoute continue
      final voiceService = Provider.of<VoiceDetectionService>(
        context,
        listen: false,
      );
      voiceService.stopListening();

      await _authService.signOut();
      Navigator.of(context).pushReplacementNamed('/login');
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur lors de la déconnexion: $e')),
      );
    }
  }

  void _navigateToTestPage() {
    Navigator.of(context).pushNamed('/test_speech');
  }

  void _toggleReminderSidebar() {
    setState(() {
      _isReminderSidebarOpen = !_isReminderSidebarOpen;
    });
  }

  @override
  void dispose() {
    // _speech.stop(); // Assure que l'écoute est arrêtée

    // Arrêter l'écoute continue
    final voiceService = Provider.of<VoiceDetectionService>(
      context,
      listen: false,
    );
    voiceService.stopListening();

    _pulseController.dispose();
    _waveController.dispose();
    _orbitController.dispose();
    _siriController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final voiceService = Provider.of<VoiceDetectionService>(context);

    return Scaffold(
      key: _scaffoldKey,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text('Assistant Vocal'),
        actions: [
          // Bouton pour les paramètres de thème
          ThemeSwitcher(),

          // Nouveau bouton pour diagnostiquer/réinitialiser la reconnaissance vocale
          IconButton(
            icon: const Icon(Icons.settings_voice),
            onPressed: _showVoiceRecognitionSettings,
            tooltip: 'Paramètres de reconnaissance vocale',
          ),

          // Bouton pour ouvrir/fermer la barre latérale des rappels
          IconButton(
            icon: Icon(
              _isReminderSidebarOpen
                  ? Icons.notifications_active
                  : Icons.notifications,
            ),
            onPressed: () {
              setState(() {
                _isReminderSidebarOpen = !_isReminderSidebarOpen;
              });
            },
            tooltip: 'Rappels',
          ),

          // Bouton de déconnexion
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _signOut,
            tooltip: 'Déconnexion',
          ),
        ],
      ),
      body: Stack(
        children: [
          // Fond décoratif
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
              ),
              child: Stack(
                children: [
                  Positioned(
                    top: -100,
                    right: -100,
                    child: Container(
                      width: 300,
                      height: 300,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withOpacity(0.05),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -80,
                    left: -80,
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(
                          context,
                        ).colorScheme.secondary.withOpacity(0.05),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Contenu principal - caché quand la barre latérale est ouverte
          if (!_isReminderSidebarOpen)
            SafeArea(
              child: Column(
                children: [
                  // Corps principal
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Texte de bienvenue ou texte reconnu
                          if (_showWelcomeMessage)
                            const Text(
                              'Bonjour ! Comment puis-je vous aider ?',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            )
                          else if (_recognizedText.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Text(
                                _recognizedText,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),

                          const SizedBox(height: 20),

                          // Affichage des informations météo quand disponibles
                          if (_showWeatherInfo)
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 500),
                              curve: Curves.easeInOut,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 10,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child:
                                  _isLoadingWeather
                                      ? const CircularProgressIndicator()
                                      : _weatherData != null
                                      ? _buildWeatherDisplay()
                                      : _forecastData != null
                                      ? _buildForecastDisplay()
                                      : const SizedBox(),
                            ),

                          const SizedBox(height: 20),

                          // Nouveau design du bouton d'assistant vocal
                          GestureDetector(
                            onTap: _toggleListening,
                            child: Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: context
                                    .watch<ThemeService>()
                                    .primaryColor
                                    .withOpacity(_isListening ? 1.0 : 0.7),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    spreadRadius: 2,
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: IconButton(
                                icon: Icon(
                                  _isListening ? Icons.mic : Icons.mic_none,
                                  size: 40,
                                  color: Colors.white,
                                ),
                                onPressed: _toggleListening,
                              ),
                            ),
                          ),

                          const SizedBox(height: 20),

                          // Texte d'état
                          Text(
                            _isListening
                                ? 'Écoute en cours...'
                                : 'Appuyez pour activer l\'assistant',
                            style: TextStyle(
                              fontSize: 16,
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Barre latérale des rappels - prend tout l'écran quand elle est ouverte
          if (_isReminderSidebarOpen)
            Positioned(
              top: 0,
              right: 0,
              bottom: 0,
              left: 0,
              child: RemindersSidebar(
                onAddReminder: _addReminderWithNotification,
                onClose: () => setState(() => _isReminderSidebarOpen = false),
              ),
            ),
        ],
      ),
    );
  }

  // Widget pour afficher les informations météo actuelles
  Widget _buildWeatherDisplay() {
    if (_weatherData == null) return const SizedBox();
    return WeatherWidget(weatherData: _weatherData!);
  }

  // Widget pour afficher les prévisions
  Widget _buildForecastDisplay() {
    if (_forecastData == null || _forecastData!.isEmpty)
      return const SizedBox();

    // Utiliser les widgets de ForecastWidget depuis notre fichier weather_widget.dart
    return ForecastWidget(
      forecastData: _forecastData!,
      dayOffset: _forecastDayOffset,
    );
  }

  // Vérifier les rappels à venir
  Future<void> _checkForUpcomingReminders() async {
    // Cette méthode vérifie s'il y a des rappels très proches (dans les 2 minutes)
    // et les annonce automatiquement
    final now = DateTime.now();

    try {
      final reminders = await _remindersService.getUserRemindersOnce();

      // Trouver les rappels qui sont à moins de 2 minutes de l'heure actuelle
      final imminentReminders =
          reminders.where((reminder) {
            if (reminder.isCompleted) return false;

            final difference =
                reminder.dateTime.difference(now).inMinutes.abs();
            return difference <
                2; // Si à moins de 2 minutes de l'heure actuelle
          }).toList();

      if (imminentReminders.isNotEmpty) {
        // Désactiver temporairement l'écoute vocale pour éviter les interférences
        final voiceService = Provider.of<VoiceDetectionService>(
          context,
          listen: false,
        );
        voiceService.continuousListening = false;

        try {
          // Si des rappels sont imminents, ouvrir la barre latérale
          setState(() {
            _isReminderSidebarOpen = true;
          });

          // Annoncer les rappels imminents
          for (final reminder in imminentReminders) {
            await _ttsService.announceReminder(
              reminder.title,
              body: "Ce rappel est prévu pour maintenant.",
            );

            // Pause entre les annonces
            await Future.delayed(const Duration(milliseconds: 500));
          }
        } finally {
          // Ne pas réactiver l'écoute automatiquement
          // L'utilisateur devra appuyer sur le bouton pour réactiver
        }
      }
    } catch (e) {
      print('Erreur lors de la vérification des rappels imminents: $e');
    }
  }

  // Nouvelle méthode pour lire tous les rappels à venir
  Future<void> _readUpcomingReminders() async {
    // Récupérer les rappels
    final reminders = await _remindersService.getUserRemindersOnce();

    // Filtrer pour obtenir uniquement les rappels à venir
    final now = DateTime.now();
    final upcomingReminders =
        reminders
            .where(
              (reminder) =>
                  !reminder.isCompleted && reminder.dateTime.isAfter(now),
            )
            .toList();

    // Trier par date chronologique
    upcomingReminders.sort((a, b) => a.dateTime.compareTo(b.dateTime));

    if (upcomingReminders.isEmpty) {
      // Informer l'utilisateur qu'il n'y a pas de rappels à venir
      _ttsService.speak("Vous n'avez aucun rappel à venir.");
      return;
    }

    // Construire le texte à lire
    final reminderCount = upcomingReminders.length;
    String introText = "Voici vos ${reminderCount} prochains rappels: ";
    if (reminderCount == 1) {
      introText = "Voici votre prochain rappel: ";
    }

    await _ttsService.speak(introText);

    // Pause entre l'introduction et la liste
    await Future.delayed(const Duration(milliseconds: 500));

    // Lire chaque rappel avec une pause entre eux
    for (int i = 0; i < upcomingReminders.length; i++) {
      final reminder = upcomingReminders[i];
      final formattedDate =
          "${reminder.dateTime.day}/${reminder.dateTime.month} à ${reminder.dateTime.hour}h${reminder.dateTime.minute > 0 ? reminder.dateTime.minute : ''}";

      await _ttsService.speak(
        "${i + 1}: ${reminder.title}, prévu pour le $formattedDate",
      );

      // Pause entre les rappels
      if (i < upcomingReminders.length - 1) {
        await Future.delayed(const Duration(milliseconds: 800));
      }
    }
  }

  // NOUVELLE MÉTHODE: Recherche web
  Future<void> _performWebSearch(String query) async {
    // Importer le service de recherche web
    final webSearchService = WebSearchService();
    final tts = TtsService();

    // Annoncer que la recherche commence
    tts.speak("Recherche en cours pour: $query");

    // Effectuer la recherche
    final results = await webSearchService.search(query);

    // Lire les résultats
    if (results.isEmpty) {
      tts.speak("Je n'ai trouvé aucun résultat pour votre recherche.");
    } else {
      await tts.announceSearchResults(results);
    }
  }

  // NOUVELLE MÉTHODE: Lancer une application musicale
  Future<void> _launchMusicApp({String? query, String? app}) async {
    final musicService = MusicService();
    final tts = TtsService();

    // Vérifier si le service est disponible
    if (!musicService.isMusicServiceAvailable()) {
      tts.speak("Le service musical n'est pas disponible sur cet appareil.");
      return;
    }

    // Message avant de lancer l'application
    if (query != null && query.isNotEmpty) {
      tts.speak("Lancement de la musique pour: $query");
    } else {
      tts.speak("Lancement de l'application musicale");
    }

    // Lancer l'application musicale
    final success = await musicService.playMusic(query: query, app: app);

    if (!success) {
      tts.speak(
        "Je n'ai pas pu lancer l'application musicale. Assurez-vous qu'une application de musique est installée.",
      );
    }
  }

  // NOUVELLE MÉTHODE: Démarrer la navigation
  Future<void> _launchNavigation(String destination) async {
    _ttsService.speak("Je vous guide vers $destination");

    // Naviguer vers la page de cartes avec la destination
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MapsPage(destination: destination),
      ),
    );
  }

  // NOUVELLE MÉTHODE: Obtenir la météo pour une ville
  Future<void> _getWeatherForCity(String? city) async {
    setState(() {
      _isLoadingWeather = true;
      _weatherData = null;
    });

    final weatherService = WeatherService();
    final tts = TtsService();

    // Si aucune ville n'est fournie, utiliser la position actuelle
    if (city == null) {
      return _getWeatherForCurrentLocation();
    }

    // Message d'attente
    tts.speak("Je recherche la météo pour $city. Un instant...");

    // Obtenir les données météo
    final weatherData = await weatherService.getWeatherForCity(city);

    if (weatherData == null) {
      tts.speak(
        "Désolé, je n'ai pas pu obtenir les informations météo pour $city.",
      );
      setState(() {
        _isLoadingWeather = false;
      });
      return;
    }

    // Mise à jour de l'UI
    setState(() {
      _weatherData = weatherData;
      _isLoadingWeather = false;
      _showWeatherInfo = true;
    });

    // Générer et annoncer la description météo
    final description = weatherService.getWeatherDescription(weatherData);
    await tts.speak(description);

    // Ajouter des informations supplémentaires si pertinent
    if (weatherData.humidity > 80) {
      await Future.delayed(const Duration(milliseconds: 800));
      await tts.speak("L'humidité est très élevée à ${weatherData.humidity}%.");
    }

    if (weatherData.windSpeed > 20) {
      await Future.delayed(const Duration(milliseconds: 800));
      await tts.speak(
        "Attention, le vent souffle fort à ${weatherData.windSpeed.round()} km/h.",
      );
    }

    // Cacher les informations météo après un délai
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted) {
        setState(() {
          _showWeatherInfo = false;
        });
      }
    });
  }

  // NOUVELLE MÉTHODE: Obtenir la météo pour la position actuelle
  Future<void> _getWeatherForCurrentLocation() async {
    setState(() {
      _isLoadingWeather = true;
      _weatherData = null;
    });

    final weatherService = WeatherService();
    final tts = TtsService();

    // Message d'attente
    tts.speak(
      "Je recherche la météo pour votre position actuelle. Un instant...",
    );

    // Obtenir les données météo
    final weatherData = await weatherService.getWeatherForCurrentLocation();

    if (weatherData == null) {
      tts.speak(
        "Désolé, je n'ai pas pu obtenir les informations météo pour votre position. Vérifiez que la localisation est activée.",
      );
      setState(() {
        _isLoadingWeather = false;
      });
      return;
    }

    // Mise à jour de l'UI
    setState(() {
      _weatherData = weatherData;
      _isLoadingWeather = false;
      _showWeatherInfo = true;
    });

    // Générer et annoncer la description météo
    final description = weatherService.getWeatherDescription(weatherData);
    await tts.speak(description);

    // Ajouter des informations supplémentaires si pertinent
    if (weatherData.humidity > 80) {
      await Future.delayed(const Duration(milliseconds: 800));
      await tts.speak("L'humidité est très élevée à ${weatherData.humidity}%.");
    }

    if (weatherData.windSpeed > 20) {
      await Future.delayed(const Duration(milliseconds: 800));
      await tts.speak(
        "Attention, le vent souffle fort à ${weatherData.windSpeed.round()} km/h.",
      );
    }

    // Cacher les informations météo après un délai
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted) {
        setState(() {
          _showWeatherInfo = false;
        });
      }
    });
  }

  // NOUVELLE MÉTHODE: Obtenir les prévisions pour un jour spécifique
  Future<void> _getForecastForDay(String? city, int dayOffset) async {
    setState(() {
      _isLoadingWeather = true;
      _forecastData = null;
    });

    final weatherService = WeatherService();
    final tts = TtsService();

    final cityName = city ?? "votre position";
    // Message d'attente
    if (dayOffset == 1) {
      tts.speak(
        "Je recherche les prévisions météo pour demain à $cityName. Un instant...",
      );
    } else {
      tts.speak(
        "Je recherche les prévisions météo pour dans $dayOffset jours à $cityName. Un instant...",
      );
    }

    late List<ForecastData>? forecastList;

    // Si aucune ville n'est spécifiée, utiliser la position actuelle
    if (city == null) {
      // Obtenir les coordonnées actuelles
      final mapsService = MapsService();
      final currentPosition = await mapsService.getCurrentPosition();
      if (currentPosition != null) {
        forecastList = await weatherService.getForecastByCoordinates(
          currentPosition.latitude,
          currentPosition.longitude,
        );
      } else {
        forecastList = null;
      }
    } else {
      forecastList = await weatherService.getForecast(city);
    }

    if (forecastList == null || forecastList.isEmpty) {
      final locationText = city ?? "votre position actuelle";
      tts.speak(
        "Désolé, je n'ai pas pu obtenir les prévisions météo pour $locationText.",
      );
      setState(() {
        _isLoadingWeather = false;
      });
      return;
    }

    // Mise à jour de l'UI avec la prévision du jour demandé
    setState(() {
      _forecastData = forecastList;
      _forecastDayOffset = dayOffset;
      _isLoadingWeather = false;
      _showWeatherInfo = true;
    });

    // Générer et annoncer la description des prévisions
    final description = weatherService.getForecastDescription(
      forecastList,
      dayOffset,
    );
    await tts.speak(description);

    // Cacher les informations météo après un délai
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted) {
        setState(() {
          _showWeatherInfo = false;
        });
      }
    });
  }

  // NOUVELLE MÉTHODE: Raconter une blague
  Future<void> _tellJoke() async {
    final jokeService = JokeService();
    final tts = TtsService();

    tts.speak("Voici une blague pour vous...");

    // Obtenir une blague aléatoire
    final joke = await jokeService.getRandomJoke();

    if (joke == null) {
      tts.speak(
        "Désolé, je n'ai pas de blague à vous raconter pour le moment.",
      );
      return;
    }

    // Pause dramatique
    await Future.delayed(const Duration(milliseconds: 800));

    // Dire la blague
    await tts.speak(joke.joke);
  }

  // Helper pour extraire le nom de la ville
  String _extractCity(String text) {
    // Extraire le premier mot (la ville)
    final words = text.split(' ');
    if (words.isEmpty) return 'Paris';

    // Capitaliser la première lettre
    final cityName = words[0].trim();
    if (cityName.isEmpty) return 'Paris';

    return cityName[0].toUpperCase() + cityName.substring(1).toLowerCase();
  }

  // Nouvelle méthode pour extraire la ville d'une commande météo
  String? _extractCityFromWeatherCommand(String command) {
    // Patterns pour différentes formulations de requêtes météo
    final patterns = [
      RegExp(r'météo\s+(?:à|pour|de|dans|en)\s+([A-Za-zÀ-ÿ]+)'),
      RegExp(r'temps\s+(?:à|pour|de|dans|en)\s+([A-Za-zÀ-ÿ]+)'),
      RegExp(r'(?:à|pour|de|dans|en)\s+([A-Za-zÀ-ÿ]+)'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(command);
      if (match != null && match.groupCount >= 1) {
        final city = match.group(1);
        if (city != null && city.length > 2) {
          // Capitaliser la première lettre
          return city[0].toUpperCase() + city.substring(1).toLowerCase();
        }
      }
    }

    // Aucune ville trouvée, retourne null pour utiliser la position actuelle
    return null;
  }

  // Nouvelle méthode pour afficher les options de reconnaissance vocale
  void _showVoiceRecognitionSettings() {
    final voiceService = Provider.of<VoiceDetectionService>(
      context,
      listen: false,
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Paramètres de reconnaissance vocale'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'État actuel: ${voiceService.state == VoiceState.listening ? 'Écoute en cours' : 'Inactif'}',
                ),
                const SizedBox(height: 10),
                const Text(
                  'Si la reconnaissance vocale s\'arrête et redémarre trop souvent, '
                  'essayez de la réinitialiser.',
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _resetVoiceRecognition();
                },
                child: const Text('Réinitialiser'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _diagnoseSpeechIssues();
                },
                child: const Text('Diagnostiquer'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  // Nouvelle méthode pour réinitialiser la reconnaissance vocale
  Future<void> _resetVoiceRecognition() async {
    setState(() {
      _recognizedText = 'Réinitialisation en cours...';
    });

    final voiceService = Provider.of<VoiceDetectionService>(
      context,
      listen: false,
    );

    // Arrêter l'écoute actuelle
    if (voiceService.continuousListening) {
      voiceService.toggleContinuousListening();
    }

    // Réinitialiser le service de reconnaissance vocale
    bool success = await VoiceDiagnostics.resetVoiceRecognition();

    // Afficher le résultat
    if (success) {
      _ttsService.speak('Réinitialisation effectuée. Essayez à nouveau.');
      setState(() {
        _recognizedText =
            'Réinitialisation terminée. Veuillez redémarrer l\'écoute.';
      });
    } else {
      _ttsService.speak('Échec de la réinitialisation.');
      setState(() {
        _recognizedText = 'Échec de la réinitialisation.';
      });
    }
  }

  // Nouvelle méthode pour diagnostiquer les problèmes de reconnaissance vocale
  Future<void> _diagnoseSpeechIssues() async {
    setState(() {
      _recognizedText = 'Diagnostic en cours...';
    });

    // Générer un rapport de diagnostic
    final report = await VoiceDiagnostics.generateDiagnosticReport();

    // Afficher le rapport dans une boîte de dialogue
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Rapport de diagnostic'),
            content: SingleChildScrollView(child: Text(report)),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );

    setState(() {
      _recognizedText = 'Diagnostic terminé.';
    });
  }
}

// Peintre personnalisé pour l'effet de vague Siri
class SiriWavePainter extends CustomPainter {
  final double amplitude;
  final AnimationController waveController;

  SiriWavePainter({required this.amplitude, required this.waveController});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withOpacity(0.8)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

    final width = size.width;
    final height = size.height;
    final center = Offset(width / 2, height / 2);
    final radius = width / 2;

    // Dessiner plusieurs courbes de Bézier pour un effet de vague
    for (int i = 0; i < 4; i++) {
      final path = Path();
      final startAngle = (i * math.pi / 2) + (waveController.value * math.pi);
      final waveHeight = 5 + (amplitude * 15);

      path.moveTo(
        center.dx + radius * math.cos(startAngle),
        center.dy + radius * math.sin(startAngle),
      );

      for (
        double angle = startAngle;
        angle < startAngle + 2 * math.pi;
        angle += math.pi / 12
      ) {
        final variation =
            math.sin(angle * 3 + (waveController.value * math.pi * 2)) *
            waveHeight;
        final waveRadius = radius + variation;

        path.lineTo(
          center.dx + waveRadius * math.cos(angle),
          center.dy + waveRadius * math.sin(angle),
        );
      }

      // Fermer le chemin
      path.close();

      // Dessiner avec une opacité variable
      paint.color = Colors.white.withOpacity(0.5 - (i * 0.1));
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant SiriWavePainter oldDelegate) {
    return oldDelegate.amplitude != amplitude ||
        oldDelegate.waveController.value != waveController.value;
  }
}

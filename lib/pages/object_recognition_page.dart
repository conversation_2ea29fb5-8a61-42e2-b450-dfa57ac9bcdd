import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import 'package:voice_assistant/services/object_recognition_service.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'package:voice_assistant/services/tts_service.dart';

class ObjectRecognitionPage extends StatefulWidget {
  const ObjectRecognitionPage({super.key});

  @override
  State<ObjectRecognitionPage> createState() => _ObjectRecognitionPageState();
}

class _ObjectRecognitionPageState extends State<ObjectRecognitionPage>
    with TickerProviderStateMixin {
  late ObjectRecognitionService _objectService;
  late TTSService _ttsService;
  
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  bool _isInitializing = true;
  String _statusMessage = 'Initialisation...';
  bool _hasPermission = false;

  @override
  void initState() {
    super.initState();
    _objectService = ObjectRecognitionService();
    _ttsService = TTSService();
    
    // Animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeIn),
    );

    _pulseController.repeat(reverse: true);
    _fadeController.forward();
    
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      setState(() {
        _statusMessage = 'Demande d\'autorisation caméra...';
      });

      await _objectService.initialize();
      
      if (_objectService.isInitialized) {
        setState(() {
          _hasPermission = true;
          _isInitializing = false;
          _statusMessage = 'Prêt pour la détection';
        });
        
        await _ttsService.speak('Reconnaissance d\'objets prête');
      } else {
        setState(() {
          _hasPermission = false;
          _isInitializing = false;
          _statusMessage = 'Permission caméra refusée';
        });
      }
    } catch (e) {
      setState(() {
        _isInitializing = false;
        _statusMessage = 'Erreur: ${e.toString()}';
      });
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    _objectService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final theme = themeService.getThemeData(context);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Reconnaissance d\'Objets',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.black,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (_hasPermission && !_isInitializing)
            Consumer<ObjectRecognitionService>(
              builder: (context, service, child) {
                return IconButton(
                  icon: Icon(
                    service.isDetecting ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                  ),
                  onPressed: () async {
                    if (service.isDetecting) {
                      await service.stopDetection();
                      await _ttsService.speak('Détection arrêtée');
                    } else {
                      await service.startDetection();
                      await _ttsService.speak('Détection démarrée');
                    }
                  },
                );
              },
            ),
        ],
      ),
      body: _buildBody(theme),
      floatingActionButton: _hasPermission && !_isInitializing
          ? _buildFloatingActionButton()
          : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isInitializing) {
      return _buildLoadingState();
    }

    if (!_hasPermission) {
      return _buildPermissionState();
    }

    return _buildCameraView();
  }

  Widget _buildLoadingState() {
    return Center(
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ScaleTransition(
              scale: _pulseAnimation,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      Colors.blue.withOpacity(0.3),
                      Colors.purple.withOpacity(0.3),
                    ],
                  ),
                ),
                child: const Icon(
                  Icons.camera_alt,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 32),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 16),
            Text(
              _statusMessage,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.camera_alt_outlined,
              size: 80,
              color: Colors.white54,
            ),
            const SizedBox(height: 24),
            const Text(
              'Permission Caméra Requise',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              _statusMessage,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _initializeCamera,
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraView() {
    return Consumer<ObjectRecognitionService>(
      builder: (context, service, child) {
        if (!service.isCameraReady) {
          return _buildLoadingState();
        }

        return Stack(
          children: [
            // Aperçu caméra
            Positioned.fill(
              child: CameraPreview(service.cameraController!),
            ),
            
            // Overlay de détection
            if (service.isDetecting)
              Positioned.fill(
                child: CustomPaint(
                  painter: DetectionPainter(service.detectedObjects),
                ),
              ),
            
            // Informations en haut
            Positioned(
              top: 16,
              left: 16,
              right: 16,
              child: _buildInfoPanel(service),
            ),
            
            // Liste des objets détectés en bas
            if (service.detectedObjects.isNotEmpty)
              Positioned(
                bottom: 100,
                left: 16,
                right: 16,
                child: _buildDetectedObjectsList(service.detectedObjects),
              ),
          ],
        );
      },
    );
  }

  Widget _buildInfoPanel(ObjectRecognitionService service) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          if (service.isDetecting)
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
            ),
          if (service.isDetecting) const SizedBox(width: 8),
          Text(
            service.isDetecting ? 'Détection active' : 'Détection arrêtée',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Text(
            '${service.detectedObjects.length} objets',
            style: const TextStyle(
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetectedObjectsList(List<DetectedObject> objects) {
    return Container(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: objects.length,
        itemBuilder: (context, index) {
          final object = objects[index];
          return Container(
            margin: const EdgeInsets.only(right: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: object.color,
                width: 2,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  object.frenchName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${(object.confidence * 100).toInt()}%',
                  style: TextStyle(
                    color: object.color,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Consumer<ObjectRecognitionService>(
      builder: (context, service, child) {
        return FloatingActionButton.large(
          onPressed: () async {
            final detections = await service.detectFromPhoto();
            if (detections.isNotEmpty) {
              final objectNames = detections
                  .map((obj) => obj.frenchName)
                  .join(', ');
              await _ttsService.speak('J\'ai détecté: $objectNames');
            } else {
              await _ttsService.speak('Aucun objet détecté');
            }
          },
          backgroundColor: Colors.blue,
          child: const Icon(
            Icons.camera,
            size: 32,
            color: Colors.white,
          ),
        );
      },
    );
  }
}

// Painter pour dessiner les boîtes de détection
class DetectionPainter extends CustomPainter {
  final List<DetectedObject> objects;

  DetectionPainter(this.objects);

  @override
  void paint(Canvas canvas, Size size) {
    for (final object in objects) {
      final paint = Paint()
        ..color = object.color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3;

      final rect = Rect.fromLTWH(
        object.boundingBox.left * size.width,
        object.boundingBox.top * size.height,
        object.boundingBox.width * size.width,
        object.boundingBox.height * size.height,
      );

      canvas.drawRect(rect, paint);

      // Dessiner le label
      final textPainter = TextPainter(
        text: TextSpan(
          text: '${object.frenchName} ${(object.confidence * 100).toInt()}%',
          style: TextStyle(
            color: object.color,
            fontSize: 14,
            fontWeight: FontWeight.bold,
            backgroundColor: Colors.black.withOpacity(0.7),
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(rect.left, rect.top - textPainter.height - 4),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

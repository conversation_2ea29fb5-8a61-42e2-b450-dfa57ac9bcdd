import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/services/theme_service.dart';

class AiChatPage extends StatelessWidget {
  const AiChatPage({super.key});

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chat IA'),
        backgroundColor: themeService.getThemeData(context).primaryColor,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.psychology_alt, // Icône de cerveau pour IA
              size: 100,
              color: themeService.getThemeData(context).colorScheme.secondary,
            ),
            const SizedBox(height: 20),
            Text(
              'Page de Chat IA',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color:
                    themeService
                        .getThemeData(context)
                        .textTheme
                        .bodyLarge
                        ?.color,
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40.0),
              child: Text(
                'Ici, vous pourrez converser avec notre intelligence artificielle.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color:
                      themeService
                          .getThemeData(context)
                          .textTheme
                          .bodyMedium
                          ?.color,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

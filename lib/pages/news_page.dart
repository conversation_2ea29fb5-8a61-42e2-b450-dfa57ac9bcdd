import 'package:flutter/material.dart';
import 'package:voice_assistant/services/alpha_vantage_news_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/voice_command_service.dart';

/// Page de test pour la fonctionnalité d'actualités
class NewsPage extends StatefulWidget {
  const NewsPage({Key? key}) : super(key: key);

  @override
  State<NewsPage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> {
  final AlphaVantageNewsService _newsService = AlphaVantageNewsService.instance;
  final TtsService _ttsService = TtsService();
  final VoiceCommandService _voiceCommandService = VoiceCommandService();

  List<AlphaVantageNewsArticle> _articles = [];
  String _selectedCategory = 'general';
  bool _isLoading = false;
  String? _lastSummary;

  @override
  void initState() {
    super.initState();
    _ttsService.initialize();
    if (_newsService.isAvailable) {
      _loadNews();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Actualités'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Instructions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Commandes vocales supportées :\n'
                      '• "Lis-moi les actualités"\n'
                      '• "Quoi de neuf ?"\n'
                      '• "Actualités tech"\n'
                      '• "Infos sport"\n'
                      '\nOu utilisez les contrôles ci-dessous :',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Statut du service et contrôles
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Service d\'actualités',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Statut
                    Row(
                      children: [
                        Icon(
                          _newsService.isAvailable
                              ? Icons.check_circle
                              : Icons.error,
                          color:
                              _newsService.isAvailable
                                  ? Colors.green
                                  : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _newsService.isAvailable
                                ? 'Service configuré et prêt'
                                : 'Service non configuré - Ajoutez votre clé API NewsAPI',
                            style: TextStyle(
                              color:
                                  _newsService.isAvailable
                                      ? Colors.green
                                      : Colors.red,
                            ),
                          ),
                        ),
                      ],
                    ),

                    if (_newsService.isAvailable) ...[
                      const SizedBox(height: 16),

                      // Sélecteur de catégorie
                      DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'Catégorie',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'general',
                            child: Text('Général'),
                          ),
                          DropdownMenuItem(
                            value: 'technology',
                            child: Text('Technologie'),
                          ),
                          DropdownMenuItem(
                            value: 'sports',
                            child: Text('Sport'),
                          ),
                          DropdownMenuItem(
                            value: 'business',
                            child: Text('Business'),
                          ),
                          DropdownMenuItem(
                            value: 'health',
                            child: Text('Santé'),
                          ),
                          DropdownMenuItem(
                            value: 'science',
                            child: Text('Science'),
                          ),
                          DropdownMenuItem(
                            value: 'entertainment',
                            child: Text('Divertissement'),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedCategory = value;
                            });
                            _loadNews();
                          }
                        },
                      ),

                      const SizedBox(height: 16),

                      // Boutons d'action
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _isLoading ? null : _loadNews,
                              icon:
                                  _isLoading
                                      ? const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      )
                                      : const Icon(Icons.refresh),
                              label: Text(
                                _isLoading ? 'Chargement...' : 'Actualiser',
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton.icon(
                            onPressed:
                                _articles.isNotEmpty && !_isLoading
                                    ? _generateSummary
                                    : null,
                            icon: const Icon(Icons.summarize),
                            label: const Text('Résumer'),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Résumé généré
            if (_lastSummary != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Résumé vocal',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),

                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(_lastSummary!),
                      ),

                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: _speakSummary,
                        icon: const Icon(Icons.volume_up),
                        label: const Text('Écouter le résumé'),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Liste des articles
            Expanded(
              child: Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'Articles (${_articles.length})',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ),

                    Expanded(
                      child:
                          _articles.isEmpty
                              ? const Center(
                                child: Text('Aucun article chargé'),
                              )
                              : ListView.builder(
                                itemCount: _articles.length,
                                itemBuilder: (context, index) {
                                  final article = _articles[index];
                                  return ListTile(
                                    title: Text(
                                      article.title,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          article.summary.length > 100
                                              ? '${article.summary.substring(0, 100)}...'
                                              : article.summary,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          '${article.source} • ${article.timePublished}',
                                          style:
                                              Theme.of(
                                                context,
                                              ).textTheme.bodySmall,
                                        ),
                                      ],
                                    ),
                                    leading: Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Icon(
                                        Icons.article,
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                        size: 30,
                                      ),
                                    ),
                                    isThreeLine: true,
                                  );
                                },
                              ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Bouton de test des commandes vocales
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Test des commandes vocales',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Testez les commandes vocales depuis la page principale',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _testVoiceCommand,
                      icon: const Icon(Icons.mic),
                      label: const Text('Tester une commande'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _loadNews() async {
    if (!_newsService.isAvailable || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final articles = await _newsService.getNews(
        topics: _selectedCategory,
        limit: 10,
      );

      setState(() {
        _articles = articles ?? [];
        _isLoading = false;
        _lastSummary = null; // Reset summary when loading new articles
      });

      if (articles == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors du chargement des actualités'),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
    }
  }

  Future<void> _generateSummary() async {
    if (_articles.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final summary = await _newsService.generateNewsSummary(_articles);

      setState(() {
        _lastSummary = summary;
        _isLoading = false;
      });

      if (summary == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Impossible de générer le résumé')),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
    }
  }

  Future<void> _speakSummary() async {
    if (_lastSummary == null) return;

    try {
      await _ttsService.speak(_lastSummary!);
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur TTS: $e')));
    }
  }

  Future<void> _testVoiceCommand() async {
    // Tester une commande vocale prédéfinie
    const testCommand = 'lis-moi les actualités';

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Test de la commande: "$testCommand"')),
    );

    try {
      final processed = await _voiceCommandService.processCommand(testCommand);

      if (!processed) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Commande non reconnue')));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
    }
  }
}
